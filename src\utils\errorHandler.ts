import { toast } from 'sonner';

export interface ErrorInfo {
  code: string;
  message: string;
  userMessage: string;
  shouldRetry: boolean;
}

export const getErrorInfo = (error: any): ErrorInfo => {
  // Firebase Auth Errors
  if (error.code?.startsWith('auth/')) {
    switch (error.code) {
      case 'auth/user-not-found':
        return {
          code: error.code,
          message: error.message,
          userMessage: 'Email tidak terdaftar',
          shouldRetry: false
        };
      case 'auth/wrong-password':
        return {
          code: error.code,
          message: error.message,
          userMessage: 'Password salah',
          shouldRetry: false
        };
      case 'auth/network-request-failed':
        return {
          code: error.code,
          message: error.message,
          userMessage: 'Koneksi internet bermasalah. Coba lagi.',
          shouldRetry: true
        };
      default:
        return {
          code: error.code,
          message: error.message,
          userMessage: 'Gagal login. Coba lagi.',
          shouldRetry: true
        };
    }
  }

  // Firestore Errors
  if (error.code?.startsWith('firestore/')) {
    switch (error.code) {
      case 'firestore/permission-denied':
        return {
          code: error.code,
          message: error.message,
          userMessage: '<PERSON><PERSON><PERSON> ditolak. Login ulang.',
          shouldRetry: false
        };
      case 'firestore/unavailable':
        return {
          code: error.code,
          message: error.message,
          userMessage: 'Server sedang bermasalah. Coba lagi.',
          shouldRetry: true
        };
      case 'firestore/deadline-exceeded':
        return {
          code: error.code,
          message: error.message,
          userMessage: 'Koneksi timeout. Coba lagi.',
          shouldRetry: true
        };
      default:
        return {
          code: error.code,
          message: error.message,
          userMessage: 'Gagal menyimpan data. Coba lagi.',
          shouldRetry: true
        };
    }
  }

  // Validation Errors
  if (error.message?.includes('VALIDASI GAGAL')) {
    return {
      code: 'validation-error',
      message: error.message,
      userMessage: error.message.replace('❌ VALIDASI GAGAL: ', ''),
      shouldRetry: false
    };
  }

  // Network Errors
  if (error.message?.includes('network') || error.message?.includes('fetch')) {
    return {
      code: 'network-error',
      message: error.message,
      userMessage: 'Koneksi internet bermasalah. Periksa koneksi Anda.',
      shouldRetry: true
    };
  }

  // Generic Error
  return {
    code: 'unknown-error',
    message: error.message || 'Unknown error',
    userMessage: 'Terjadi kesalahan. Coba lagi atau hubungi admin.',
    shouldRetry: true
  };
};

export const handleError = (error: any, context?: string) => {
  const errorInfo = getErrorInfo(error);
  
  console.error(`❌ ERROR in ${context || 'Unknown'}:`, {
    code: errorInfo.code,
    message: errorInfo.message,
    userMessage: errorInfo.userMessage,
    shouldRetry: errorInfo.shouldRetry,
    timestamp: new Date().toISOString()
  });

  // Show user-friendly message
  toast.error(errorInfo.userMessage, {
    duration: errorInfo.shouldRetry ? 5000 : 3000,
    action: errorInfo.shouldRetry ? {
      label: 'Coba Lagi',
      onClick: () => {
        // This will be handled by the calling component
        console.log('User clicked retry');
      }
    } : undefined
  });

  return errorInfo;
};

export const withRetry = async <T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  delay: number = 1000,
  context?: string
): Promise<T> => {
  let lastError: any;
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error;
      const errorInfo = getErrorInfo(error);
      
      console.warn(`⚠️ Attempt ${attempt}/${maxRetries} failed in ${context}:`, errorInfo.userMessage);
      
      // Don't retry if it's not a retryable error
      if (!errorInfo.shouldRetry) {
        throw error;
      }
      
      // Don't retry on last attempt
      if (attempt === maxRetries) {
        break;
      }
      
      // Wait before retry
      await new Promise(resolve => setTimeout(resolve, delay * attempt));
    }
  }
  
  // All retries failed
  throw lastError;
};

export const isOnline = (): boolean => {
  return navigator.onLine;
};

export const waitForOnline = (): Promise<void> => {
  return new Promise((resolve) => {
    if (isOnline()) {
      resolve();
      return;
    }
    
    const handleOnline = () => {
      window.removeEventListener('online', handleOnline);
      resolve();
    };
    
    window.addEventListener('online', handleOnline);
  });
};
