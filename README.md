# DANAPEMUDA - Aplikasi Manajemen Keuangan PEMUDA

## Tentang Aplikasi

DANAPEMUDA adalah aplikasi manajemen keuangan dan administrasi untuk organisasi pemuda. Aplikasi ini membantu pengurus PEMUDA dalam mengelola keuangan, angg<PERSON>, dan acara dengan mudah dan efisien.

## Fitur Utama

- **Manaj<PERSON>en Keuangan**: <PERSON><PERSON>u <PERSON>, pengel<PERSON>ran, dan saldo kas PEMUDA
- **Manajemen Anggota**: Kelola data anggota PEMUDA dan status pembayaran iuran
- **Manajemen Acara**: Atur jadwal acara dan kegiatan PEMUDA
- **Panel Admin**: <PERSON>kses khusus untuk pengurus PEMUDA dengan kontrol penuh terhadap data
- **WhatsApp Bot**: Kirim notifikasi otomatis ke grup WhatsApp untuk transparansi keuangan

## Teknologi yang Digunakan

Proyek ini dibangun dengan:

- Vite
- TypeScript
- React
- React Router
- React Query
- shadcn-ui
- Tailwind CSS
- Lucide React (icon)
- WhatsApp API

## Cara Menjalankan Proyek

Pastikan Anda memiliki Node.js & npm terinstal - [instal dengan nvm](https://github.com/nvm-sh/nvm#installing-and-updating)

Ikuti langkah-langkah berikut:

```sh
# Langkah 1: Clone repository
git clone <URL_GIT_ANDA>

# Langkah 2: Masuk ke direktori proyek
cd DANAPEMUDA

# Langkah 3: Instal dependensi yang diperlukan
npm install

# Langkah 4: Salin .env.example menjadi .env dan sesuaikan
cp .env.example .env

# Langkah 5: Jalankan server pengembangan
npm run dev
```

## Struktur Proyek

```
DANAPEMUDA/
├── public/              # Aset statis
├── src/                 # Kode sumber
│   ├── components/      # Komponen React
│   │   ├── ui/          # Komponen UI dari shadcn
│   ├── hooks/           # Custom hooks
│   ├── lib/             # Utilitas dan konfigurasi
│   ├── pages/           # Halaman aplikasi
│   │   ├── admin/       # Halaman admin
│   ├── services/        # Layanan dan API
│   ├── types/           # Definisi tipe TypeScript
│   └── utils/           # Fungsi utilitas
├── App.tsx              # Komponen utama
└── main.tsx            # Entry point
```

## Konfigurasi WhatsApp Bot

Untuk menggunakan fitur WhatsApp Bot, Anda perlu:

1. Menyiapkan server WhatsApp API (seperti yang didokumentasikan di file API)
2. Mengisi konfigurasi di file `.env`:
   ```
   VITE_WHATSAPP_API_URL=http://your-whatsapp-api-url
   VITE_WHATSAPP_API_KEY=your-secret-api-key
   ```
3. Mengatur WhatsApp Bot di panel admin, termasuk Session ID dan Group ID tujuan

## Kontribusi

Silakan berkontribusi dengan membuat pull request atau melaporkan issue.

## Lisensi

© NEVERTIME DANAPEMUDA. GUYUB RUKUN SALAWASE
