import React, { useEffect, useRef } from 'react';
import { X } from 'lucide-react';

interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  children: React.ReactNode;
}

const Modal: React.FC<ModalProps> = ({ isOpen, onClose, title, children }) => {
  const modalRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };
    
    // Nonaktifkan klik di luar untuk sementara
    // const handleClickOutside = (e: MouseEvent) => { ... };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      // document.addEventListener('mousedown', handleClickOutside);
      document.body.style.overflow = 'hidden'; // Kunci scroll body lagi
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      // document.removeEventListener('mousedown', handleClickOutside);
      document.body.style.overflow = 'unset'; // Lepas kunci scroll body
    };
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  return (
    // Overlay: items-start untuk posisi atas, padding atas, overflow-y-auto
    <div className="fixed inset-0 z-50 flex items-start justify-center p-4 pt-16 sm:pt-20 bg-black/50 overflow-y-auto">
      {/* Modal Content Wrapper */}
      <div
        ref={modalRef}
        // Ganti max-w-lg -> max-w-xl
        className="relative bg-white border-2 sm:border-4 border-[#5D534B] shadow-pastel w-full max-w-xl max-h-[calc(100vh-8rem)] sm:max-h-[calc(100vh-10rem)] rounded-xl sm:rounded-2xl flex flex-col animate-fade-in mb-8"
      >
        {/* Modal Header (Sticky) */}
        <div className="sticky top-0 z-10 bg-white p-3 sm:p-4 border-b-2 sm:border-b-4 border-[#5D534B] flex justify-between items-center flex-shrink-0">
          <h2 className="text-lg sm:text-xl font-bold text-[#5D534B] break-words pr-2">{title}</h2>
          <button
            onClick={onClose}
            className="border-2 sm:border-4 border-[#5D534B] p-1 hover:bg-[#FF9898] text-[#5D534B] transition-colors rounded-full flex-shrink-0"
            aria-label="Tutup dialog"
            title="Tutup"
          >
            <X size={18} className="sm:hidden" />
            <X size={20} className="hidden sm:block" />
          </button>
        </div>
        {/* Modal Body (Scrollable) */}
        <div className="flex-1 overflow-y-auto p-4 sm:p-6 min-h-0">
          {children}
        </div>
      </div>
    </div>
  );
};

export default Modal;
