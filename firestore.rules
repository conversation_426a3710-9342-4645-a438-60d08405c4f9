rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
    // TEMPORARY: Allow all reads and writes
    // GANTI DENGAN RULES YANG LEBIH SECURE SETELAH TESTING
    match /{document=**} {
      allow read, write: if true;
    }
    
    // PRODUCTION RULES (UNCOMMENT SETELAH TESTING):
    /*
    // Members collection
    match /members/{memberId} {
      allow read, write: if request.auth != null;
    }
    
    // Events collection  
    match /events/{eventId} {
      allow read: if true; // Public read
      allow write: if request.auth != null; // Auth required for write
    }
    
    // Expenses collection
    match /expenses/{expenseId} {
      allow read, write: if request.auth != null;
    }
    
    // Dues config collection
    match /duesConfig/{configId} {
      allow read: if true; // Public read
      allow write: if request.auth != null; // Auth required for write
    }
    */
  }
}
