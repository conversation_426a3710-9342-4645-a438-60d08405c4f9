rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
    // SECURE PRODUCTION RULES - AUTHENTICATION REQUIRED

    // Members collection - Auth required for all operations
    match /members/{memberId} {
      allow read, write: if request.auth != null;
    }

    // Events collection - Public read, auth required for write
    match /events/{eventId} {
      allow read: if true; // Public dapat melihat events
      allow write: if request.auth != null; // Hanya user login yang bisa edit
    }

    // Expenses collection - Auth required for all operations
    match /expenses/{expenseId} {
      allow read, write: if request.auth != null;
    }

    // Dues config collection - Public read, auth required for write
    match /dues_config/{configId} {
      allow read: if true; // Public dapat melihat konfigurasi iuran
      allow write: if request.auth != null; // Hanya admin yang bisa edit
    }

    // Deny all other collections by default
    match /{document=**} {
      allow read, write: if false;
    }
  }
}
