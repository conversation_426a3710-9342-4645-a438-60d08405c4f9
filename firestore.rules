rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
    // DEVELOPMENT RULES - ALLOW ALL ACCESS
    // WARNING: These rules are for development only!
    // Change to secure rules before production deployment

    match /{document=**} {
      allow read, write: if true;
    }

    // SECURE PRODUCTION RULES (uncomment when ready for production)
    /*
    // Members collection - Auth required for all operations
    match /members/{memberId} {
      allow read, write: if request.auth != null;
    }

    // Events collection - Public read, auth required for write
    match /events/{eventId} {
      allow read: if true; // Public dapat melihat events
      allow write: if request.auth != null; // Hanya user login yang bisa edit
    }

    // Expenses collection - Auth required for all operations
    match /expenses/{expenseId} {
      allow read, write: if request.auth != null;
    }

    // Dues config collection - Public read, auth required for write
    match /dues_config/{configId} {
      allow read: if true; // Public dapat melihat konfigurasi iuran
      allow write: if request.auth != null; // <PERSON>ya admin yang bisa edit
    }

    // Deny all other collections by default
    match /{document=**} {
      allow read, write: if false;
    }
    */
  }
}
