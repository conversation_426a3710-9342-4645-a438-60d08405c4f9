// Import the functions you need from the SDKs you need
import { initializeApp } from "firebase/app";
import { getAuth } from 'firebase/auth';
import { getFirestore, connectFirestoreEmulator, enableNetwork, disableNetwork } from 'firebase/firestore';
import { getStorage } from 'firebase/storage';

// Your web app's Firebase configuration
const firebaseConfig = {
  apiKey: import.meta.env.VITE_FIREBASE_API_KEY,
  authDomain: import.meta.env.VITE_FIREBASE_AUTH_DOMAIN,
  projectId: import.meta.env.VITE_FIREBASE_PROJECT_ID,
  storageBucket: import.meta.env.VITE_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: import.meta.env.VITE_FIREBASE_MESSAGING_SENDER_ID,
  appId: import.meta.env.VITE_FIREBASE_APP_ID,
  measurementId: import.meta.env.VITE_FIREBASE_MEASUREMENT_ID
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Analytics dinonaktifkan untuk development
// const analytics = getAnalytics(app);

// Initialize Firebase services with optimization
export const auth = getAuth(app);
export const db = getFirestore(app);
export const storage = getStorage(app);

// Chrome compatibility fix
if (typeof window !== 'undefined') {
  // Force disable cache for Chrome
  import('firebase/firestore').then(({ connectFirestoreEmulator, clearIndexedDbPersistence }) => {
    // Clear any existing cache
    clearIndexedDbPersistence(db).catch(() => {
      // Ignore errors, cache might not exist
    });
  });
}

export default app;
