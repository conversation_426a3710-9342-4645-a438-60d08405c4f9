import React from 'react';
import { Skeleton } from './ui/skeleton';

// Card skeleton for general use
export const CardSkeleton: React.FC<{ className?: string }> = ({ className = "" }) => (
  <div className={`neo-card p-4 space-y-3 ${className}`}>
    <Skeleton className="h-4 w-3/4" />
    <Skeleton className="h-4 w-1/2" />
    <Skeleton className="h-8 w-full" />
  </div>
);

// Table skeleton for data tables
export const TableSkeleton: React.FC<{ rows?: number; cols?: number }> = ({ 
  rows = 5, 
  cols = 4 
}) => (
  <div className="neo-card">
    <div className="p-4 border-b border-[#5D534B]">
      <Skeleton className="h-6 w-48" />
    </div>
    <div className="p-4 space-y-3">
      {Array.from({ length: rows }).map((_, i) => (
        <div key={i} className="flex space-x-4">
          {Array.from({ length: cols }).map((_, j) => (
            <Skeleton key={j} className="h-4 flex-1" />
          ))}
        </div>
      ))}
    </div>
  </div>
);

// Stats card skeleton
export const StatCardSkeleton: React.FC = () => (
  <div className="neo-card neo-gradient-blue p-6">
    <div className="flex items-center justify-between">
      <div className="space-y-2">
        <Skeleton className="h-4 w-24" />
        <Skeleton className="h-8 w-32" />
      </div>
      <Skeleton className="h-12 w-12 rounded-full" />
    </div>
  </div>
);

// Member card skeleton
export const MemberCardSkeleton: React.FC = () => (
  <div className="neo-card p-4 space-y-3">
    <div className="flex items-center justify-between">
      <Skeleton className="h-6 w-40" />
      <Skeleton className="h-6 w-16 rounded-full" />
    </div>
    <div className="space-y-2">
      <Skeleton className="h-4 w-32" />
      <Skeleton className="h-4 w-24" />
    </div>
    <div className="flex space-x-2">
      <Skeleton className="h-8 w-20" />
      <Skeleton className="h-8 w-20" />
    </div>
  </div>
);

// Event card skeleton
export const EventCardSkeleton: React.FC = () => (
  <div className="neo-card p-4 space-y-3">
    <div className="flex items-start justify-between">
      <div className="space-y-2 flex-1">
        <Skeleton className="h-6 w-3/4" />
        <Skeleton className="h-4 w-1/2" />
        <Skeleton className="h-4 w-2/3" />
      </div>
      <Skeleton className="h-6 w-20 rounded-full" />
    </div>
    <div className="flex items-center space-x-4 pt-2">
      <Skeleton className="h-4 w-24" />
      <Skeleton className="h-4 w-32" />
    </div>
  </div>
);

// Expense card skeleton
export const ExpenseCardSkeleton: React.FC = () => (
  <div className="expense-card space-y-3">
    <div className="flex items-center justify-between">
      <Skeleton className="h-5 w-48" />
      <Skeleton className="h-6 w-24" />
    </div>
    <div className="flex items-center justify-between text-sm">
      <Skeleton className="h-4 w-32" />
      <Skeleton className="h-4 w-20" />
    </div>
  </div>
);

// Page loading skeleton
export const PageLoadingSkeleton: React.FC<{ 
  title?: boolean;
  stats?: number;
  cards?: number;
}> = ({ 
  title = true, 
  stats = 0, 
  cards = 3 
}) => (
  <div className="space-y-6">
    {title && (
      <div className="space-y-2">
        <Skeleton className="h-8 w-64" />
        <Skeleton className="h-4 w-96" />
      </div>
    )}
    
    {stats > 0 && (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {Array.from({ length: stats }).map((_, i) => (
          <StatCardSkeleton key={i} />
        ))}
      </div>
    )}
    
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {Array.from({ length: cards }).map((_, i) => (
        <CardSkeleton key={i} />
      ))}
    </div>
  </div>
);

// Loading spinner component
export const LoadingSpinner: React.FC<{ 
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}> = ({ size = 'md', className = '' }) => {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6', 
    lg: 'w-8 h-8'
  };

  return (
    <div className={`animate-spin rounded-full border-2 border-[#5D534B] border-t-transparent ${sizeClasses[size]} ${className}`} />
  );
};

// Loading overlay for buttons
export const ButtonLoading: React.FC<{ 
  children: React.ReactNode;
  loading: boolean;
  className?: string;
}> = ({ children, loading, className = '' }) => (
  <div className={`relative ${className}`}>
    {loading && (
      <div className="absolute inset-0 flex items-center justify-center bg-white/80 rounded-full">
        <LoadingSpinner size="sm" />
      </div>
    )}
    <div className={loading ? 'opacity-50' : ''}>
      {children}
    </div>
  </div>
);

// Empty state component
export const EmptyState: React.FC<{
  title: string;
  description: string;
  action?: React.ReactNode;
  icon?: React.ReactNode;
}> = ({ title, description, action, icon }) => (
  <div className="neo-card p-8 text-center">
    {icon && (
      <div className="flex justify-center mb-4">
        {icon}
      </div>
    )}
    <h3 className="text-lg font-bold text-[#5D534B] mb-2">{title}</h3>
    <p className="text-[#5D534B] mb-4">{description}</p>
    {action && action}
  </div>
);
