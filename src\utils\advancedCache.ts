/**
 * Advanced Caching Strategy for OSIS Application
 * 
 * Features:
 * - Multi-layer caching (Memory, IndexedDB, LocalStorage)
 * - Smart cache invalidation
 * - Compression for large data
 * - Cache analytics and monitoring
 * - Automatic cleanup and optimization
 */

interface CacheEntry<T = unknown> {
  data: T;
  timestamp: number;
  ttl: number; // Time to live in milliseconds
  version: string;
  size: number;
  accessCount: number;
  lastAccessed: number;
  compressed?: boolean;
}

interface CacheConfig {
  maxMemorySize: number; // Max memory cache size in bytes
  maxIndexedDBSize: number; // Max IndexedDB size in bytes
  defaultTTL: number; // Default TTL in milliseconds
  compressionThreshold: number; // Compress data larger than this size
  cleanupInterval: number; // Cleanup interval in milliseconds
}

interface CacheStats {
  memoryHits: number;
  memoryMisses: number;
  indexedDBHits: number;
  indexedDBMisses: number;
  totalSize: number;
  entryCount: number;
  hitRate: number;
}

class AdvancedCacheManager {
  private memoryCache = new Map<string, CacheEntry>();
  private config: CacheConfig;
  private stats: CacheStats;
  private cleanupTimer?: number;
  private dbName = 'osis-cache-db';
  private dbVersion = 1;
  private db?: IDBDatabase;

  constructor(config: Partial<CacheConfig> = {}) {
    this.config = {
      maxMemorySize: 50 * 1024 * 1024, // 50MB
      maxIndexedDBSize: 200 * 1024 * 1024, // 200MB
      defaultTTL: 30 * 60 * 1000, // 30 minutes
      compressionThreshold: 100 * 1024, // 100KB
      cleanupInterval: 5 * 60 * 1000, // 5 minutes
      ...config
    };

    this.stats = {
      memoryHits: 0,
      memoryMisses: 0,
      indexedDBHits: 0,
      indexedDBMisses: 0,
      totalSize: 0,
      entryCount: 0,
      hitRate: 0
    };

    this.initializeIndexedDB();
    this.startCleanupTimer();
  }

  private async initializeIndexedDB(): Promise<void> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.dbName, this.dbVersion);

      request.onerror = () => reject(request.error);
      request.onsuccess = () => {
        this.db = request.result;
        resolve();
      };

      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;
        if (!db.objectStoreNames.contains('cache')) {
          const store = db.createObjectStore('cache', { keyPath: 'key' });
          store.createIndex('timestamp', 'timestamp');
          store.createIndex('lastAccessed', 'lastAccessed');
        }
      };
    });
  }

  private compress(data: string): string {
    // Simple compression using LZ-string-like algorithm
    // In production, consider using a proper compression library
    try {
      return btoa(encodeURIComponent(data));
    } catch {
      return data;
    }
  }

  private decompress(data: string): string {
    try {
      return decodeURIComponent(atob(data));
    } catch {
      return data;
    }
  }

  private calculateSize(data: unknown): number {
    return new Blob([JSON.stringify(data)]).size;
  }

  private isExpired(entry: CacheEntry): boolean {
    return Date.now() - entry.timestamp > entry.ttl;
  }

  private updateStats(): void {
    const totalHits = this.stats.memoryHits + this.stats.indexedDBHits;
    const totalMisses = this.stats.memoryMisses + this.stats.indexedDBMisses;
    this.stats.hitRate = totalHits / (totalHits + totalMisses) || 0;
    this.stats.entryCount = this.memoryCache.size;
    this.stats.totalSize = Array.from(this.memoryCache.values())
      .reduce((total, entry) => total + entry.size, 0);
  }

  async set<T>(key: string, data: T, ttl?: number): Promise<void> {
    const timestamp = Date.now();
    const size = this.calculateSize(data);
    const actualTTL = ttl || this.config.defaultTTL;
    
    let serializedData = JSON.stringify(data);
    let compressed = false;

    // Compress large data
    if (size > this.config.compressionThreshold) {
      serializedData = this.compress(serializedData);
      compressed = true;
    }

    const entry: CacheEntry<string> = {
      data: serializedData,
      timestamp,
      ttl: actualTTL,
      version: '1.0',
      size,
      accessCount: 0,
      lastAccessed: timestamp,
      compressed
    };

    // Store in memory cache
    this.memoryCache.set(key, entry);

    // Store in IndexedDB for persistence
    if (this.db) {
      try {
        const transaction = this.db.transaction(['cache'], 'readwrite');
        const store = transaction.objectStore('cache');
        await store.put({ key, ...entry });
      } catch (error) {
      }
    }

    // Cleanup if memory cache is too large
    await this.cleanupMemoryCache();
    this.updateStats();
  }

  async get<T>(key: string): Promise<T | null> {
    // Try memory cache first
    let entry = this.memoryCache.get(key);
    
    if (entry) {
      if (this.isExpired(entry)) {
        this.memoryCache.delete(key);
        this.stats.memoryMisses++;
      } else {
        entry.accessCount++;
        entry.lastAccessed = Date.now();
        this.stats.memoryHits++;
        
        let data = entry.data;
        if (entry.compressed) {
          data = this.decompress(data);
        }
        
        this.updateStats();
        return JSON.parse(data);
      }
    } else {
      this.stats.memoryMisses++;
    }

    // Try IndexedDB
    if (this.db) {
      try {
        const transaction = this.db.transaction(['cache'], 'readonly');
        const store = transaction.objectStore('cache');
        const request = store.get(key);
        
        const result = await new Promise<CacheEntry<string> | undefined>((resolve) => {
          request.onsuccess = () => resolve(request.result);
          request.onerror = () => resolve(undefined);
        });

        if (result && !this.isExpired(result)) {
          // Move back to memory cache
          this.memoryCache.set(key, result);
          this.stats.indexedDBHits++;
          
          let data = result.data;
          if (result.compressed) {
            data = this.decompress(data);
          }
          
          this.updateStats();
          return JSON.parse(data);
        } else {
          this.stats.indexedDBMisses++;
        }
      } catch (error) {
        this.stats.indexedDBMisses++;
      }
    }

    this.updateStats();
    return null;
  }

  async delete(key: string): Promise<void> {
    this.memoryCache.delete(key);
    
    if (this.db) {
      try {
        const transaction = this.db.transaction(['cache'], 'readwrite');
        const store = transaction.objectStore('cache');
        await store.delete(key);
      } catch (error) {
      }
    }
    
    this.updateStats();
  }

  async clear(): Promise<void> {
    this.memoryCache.clear();
    
    if (this.db) {
      try {
        const transaction = this.db.transaction(['cache'], 'readwrite');
        const store = transaction.objectStore('cache');
        await store.clear();
      } catch (error) {
      }
    }
    
    this.stats = {
      memoryHits: 0,
      memoryMisses: 0,
      indexedDBHits: 0,
      indexedDBMisses: 0,
      totalSize: 0,
      entryCount: 0,
      hitRate: 0
    };
  }

  private async cleanupMemoryCache(): Promise<void> {
    const entries = Array.from(this.memoryCache.entries());
    const totalSize = entries.reduce((sum, [, entry]) => sum + entry.size, 0);

    if (totalSize > this.config.maxMemorySize) {
      // Sort by last accessed time (LRU)
      entries.sort(([, a], [, b]) => a.lastAccessed - b.lastAccessed);
      
      // Remove oldest entries until under limit
      let currentSize = totalSize;
      for (const [key, entry] of entries) {
        if (currentSize <= this.config.maxMemorySize * 0.8) break;
        
        this.memoryCache.delete(key);
        currentSize -= entry.size;
      }
    }

    // Remove expired entries
    const now = Date.now();
    for (const [key, entry] of this.memoryCache.entries()) {
      if (this.isExpired(entry)) {
        this.memoryCache.delete(key);
      }
    }
  }

  private startCleanupTimer(): void {
    this.cleanupTimer = window.setInterval(() => {
      this.cleanupMemoryCache();
    }, this.config.cleanupInterval);
  }

  getStats(): CacheStats {
    this.updateStats();
    return { ...this.stats };
  }

  destroy(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
    }
    
    if (this.db) {
      this.db.close();
    }
  }
}

// Global cache instance
export const advancedCache = new AdvancedCacheManager();

// Cache utilities for specific data types
export const cacheUtils = {
  // Cache Firebase data with smart TTL
  cacheFirebaseData: async <T>(key: string, data: T, collection: string): Promise<void> => {
    const ttl = collection === 'members' ? 60 * 60 * 1000 : // 1 hour for members
               collection === 'events' ? 30 * 60 * 1000 : // 30 minutes for events
               15 * 60 * 1000; // 15 minutes for others
    
    await advancedCache.set(`firebase:${collection}:${key}`, data, ttl);
  },

  // Get cached Firebase data
  getCachedFirebaseData: async <T>(key: string, collection: string): Promise<T | null> => {
    return await advancedCache.get<T>(`firebase:${collection}:${key}`);
  },

  // Cache user preferences
  cacheUserPreferences: async (userId: string, preferences: unknown): Promise<void> => {
    await advancedCache.set(`user:${userId}:preferences`, preferences, 24 * 60 * 60 * 1000); // 24 hours
  },

  // Cache API responses
  cacheAPIResponse: async <T>(endpoint: string, data: T, ttl = 10 * 60 * 1000): Promise<void> => {
    await advancedCache.set(`api:${endpoint}`, data, ttl);
  },

  // Get cache statistics
  getStats: () => advancedCache.getStats(),

  // Clear all cache
  clearAll: () => advancedCache.clear()
};
