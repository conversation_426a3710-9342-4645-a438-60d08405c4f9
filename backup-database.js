// Script untuk backup database Firebase sebelum security changes
// Jalankan dengan: node backup-database.js

import { initializeApp } from 'firebase/app';
import { getFirestore, collection, getDocs } from 'firebase/firestore';
import fs from 'fs';
import path from 'path';

// Firebase config (gunakan yang sama dengan aplikasi)
const firebaseConfig = {
  apiKey: "AIzaSyCyMds8m-KKOR4537ZvY4kipd4aFUbIgyc",
  authDomain: "pemuda-psy.firebaseapp.com",
  projectId: "pemuda-psy",
  storageBucket: "pemuda-psy.firebasestorage.app",
  messagingSenderId: "792680197911",
  appId: "1:792680197911:web:59e978bba5dea4dd419152",
  measurementId: "G-LJJQNZMJ5L"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);

// Collections to backup
const collections = ['members', 'expenses', 'events', 'dues_config'];

async function backupCollection(collectionName) {
  try {
    console.log(`📦 Backing up collection: ${collectionName}`);
    
    const querySnapshot = await getDocs(collection(db, collectionName));
    const data = [];
    
    querySnapshot.forEach((doc) => {
      data.push({
        id: doc.id,
        ...doc.data()
      });
    });
    
    // Create backup directory if not exists
    const backupDir = './database-backup';
    if (!fs.existsSync(backupDir)) {
      fs.mkdirSync(backupDir);
    }
    
    // Save to JSON file
    const filename = `${collectionName}-backup-${new Date().toISOString().split('T')[0]}.json`;
    const filepath = path.join(backupDir, filename);
    
    fs.writeFileSync(filepath, JSON.stringify(data, null, 2));
    
    console.log(`✅ ${collectionName}: ${data.length} documents backed up to ${filename}`);
    return data.length;
    
  } catch (error) {
    console.error(`❌ Error backing up ${collectionName}:`, error);
    return 0;
  }
}

async function backupDatabase() {
  console.log('🚀 Starting database backup...');
  console.log('📅 Backup date:', new Date().toISOString());
  
  let totalDocuments = 0;
  
  for (const collectionName of collections) {
    const count = await backupCollection(collectionName);
    totalDocuments += count;
  }
  
  // Create backup summary
  const summary = {
    backupDate: new Date().toISOString(),
    totalCollections: collections.length,
    totalDocuments: totalDocuments,
    collections: collections,
    note: 'Backup created before implementing Firebase security rules'
  };
  
  fs.writeFileSync('./database-backup/backup-summary.json', JSON.stringify(summary, null, 2));
  
  console.log('\n🎉 Backup completed!');
  console.log(`📊 Total: ${totalDocuments} documents from ${collections.length} collections`);
  console.log('📁 Files saved in: ./database-backup/');
  console.log('\n⚠️  IMPORTANT: Keep these backup files safe!');
}

// Run backup
backupDatabase().catch(console.error);
