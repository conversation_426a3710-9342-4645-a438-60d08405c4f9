// API Services - Firebase based
// Semua operasi database menggunakan Firebase Firestore melalui Context

// Re-export types dari api.ts untuk backward compatibility
export type {
  Member,
  Event,
  Transaction
} from './api';

/**
 * CATATAN PENTING:
 *
 * Aplikasi ini menggunakan Firebase Firestore sebagai database utama.
 * Semua operasi CRUD dilakukan melalui Context providers:
 *
 * - MembersContext untuk data anggota
 * - ExpensesContext untuk data pengeluaran
 * - EventsContext untuk data acara
 * - DuesConfigContext untuk konfigurasi iuran
 * - AuthContext untuk autentikasi
 *
 * Import Context yang diperlukan:
 * import { useMembersContext } from '@/context/MembersContext';
 * import { useExpensesContext } from '@/context/ExpensesContext';
 * import { useEventsContext } from '@/context/EventsContext';
 * import { useDuesConfigContext } from '@/context/DuesConfigContext';
 * import { useAuth } from '@/context/AuthContext';
 */