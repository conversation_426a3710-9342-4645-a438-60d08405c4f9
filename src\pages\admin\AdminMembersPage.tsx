import { useState } from 'react';
import { Plus } from 'lucide-react';
import { toast } from '@/components/ui/use-toast';
import Loader from '../../components/Loader';
import { useMembersContext } from '../../context/MembersContext';
import { handleError, withRetry } from '../../utils/errorHandler';
import Pagination from '../../components/Pagination';
import MemberForm from '../../components/admin/MemberForm';
import MemberCard from '../../components/admin/MemberCard';
import MemberTable from '../../components/admin/MemberTable';
import MemberSearchFilter from '../../components/admin/MemberSearchFilter';

const AdminMembersPage = () => {
  const {
    members,
    loading,
    currentPage,
    totalPages,
    itemsPerPage,
    totalMembers,
    addMember,
    updateMember,
    deleteMember,
    toggleMemberStatus,
    setPage,
    searchMembers,
    filterMembers
  } = useMembersContext();
  const [editingId, setEditingId] = useState<string | null>(null);
  const [editValues, setEditValues] = useState<any>({});
  const [isAdding, setIsAdding] = useState(false);
  const [toggleLoading, setToggleLoading] = useState<string | null>(null);
  const [actionLoading, setActionLoading] = useState(false);

  const handleEdit = (member: any) => {
    setEditingId(member.id);
    setEditValues({ ...member });
  };

  const handleChange = (id: string, field: string, value: any) => {
    setEditValues(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleDelete = async (id: string) => {
    if (!confirm('Apakah Anda yakin ingin menghapus anggota ini?')) return;

    try {
      setActionLoading(true);
      await deleteMember(id);
      toast({
        title: "Sukses",
        description: "Data anggota berhasil dihapus",
      });
    } catch (error) {
      handleError(error, 'AdminMembersPage (admin)');
      toast({
        title: "Error",
        description: "Gagal menghapus data anggota",
        variant: "destructive",
      });
    } finally {
      setActionLoading(false);
    }
  };

  const handleAddMember = async (memberData: any) => {
    try {
      setActionLoading(true);

      await withRetry(
        () => addMember(memberData),
        3,
        1000,
        'Add Member'
      );

      setIsAdding(false);
      toast({
        title: "✅ Sukses",
        description: "Anggota berhasil ditambahkan ke database",
      });
    } catch (error) {
      handleError(error, 'Add Member');
      throw error; // Re-throw untuk handling di MemberForm
    } finally {
      setActionLoading(false);
    }
  };





  const handleToggleStatus = async (member: any) => {
    try {
      setToggleLoading(member.id);
      await toggleMemberStatus(member.id);
      toast({
        title: "Sukses",
        description: "Status pembayaran berhasil diperbarui di database",
      });
    } catch (error) {
      handleError(error, 'AdminMembersPage (admin)');
      toast({
        title: "Error",
        description: "Gagal memperbarui status pembayaran di database",
        variant: "destructive",
      });
    } finally {
      setToggleLoading(null);
    }
  };

  const handleEditSubmit = async () => {
    try {
      if (!editingId) return;

      setActionLoading(true);
      await updateMember(editingId, editValues);
      setEditingId(null);
      toast({
        title: "Sukses",
        description: "Data anggota berhasil diperbarui di database",
      });
    } catch (error) {
      handleError(error, 'AdminMembersPage (admin)');
      toast({
        title: "Error",
        description: "Gagal memperbarui data anggota di database",
        variant: "destructive",
      });
    } finally {
      setActionLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <Loader size="medium" variant="secondary" text="Memuat Data Anggota dari Database..." />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-black">Kelola Anggota</h2>
        <button
          type="button"
          className="neo-button-green text-sm flex items-center"
          onClick={() => setIsAdding(true)}
        >
          <Plus size={16} className="mr-2" />
          Tambah Anggota
        </button>
      </div>

      {/* Search and Filter */}
      <MemberSearchFilter
        totalMembers={totalMembers}
        onSearch={searchMembers}
        onFilter={filterMembers}
      />

      {/* Add Member Form */}
      {isAdding && (
        <MemberForm
          onSubmit={handleAddMember}
          onCancel={() => setIsAdding(false)}
          loading={actionLoading}
        />
      )}

      {/* Mobile Card View */}
      <div className="block md:hidden space-y-4">
        {members.map((member: any) => (
          <MemberCard
            key={member.id}
            member={member}
            isEditing={editingId === member.id}
            editValues={editValues}
            toggleLoading={toggleLoading === member.id}
            onEdit={handleEdit}
            onDelete={handleDelete}
            onToggleStatus={handleToggleStatus}
            onSaveEdit={handleEditSubmit}
            onCancelEdit={() => setEditingId(null)}
            onChange={(field, value) => handleChange(member.id, field, value)}
          />
        ))}
      </div>

      {/* Desktop Table View */}
      <MemberTable
        members={members}
        editingId={editingId}
        editValues={editValues}
        toggleLoading={toggleLoading}
        actionLoading={actionLoading}
        onEdit={handleEdit}
        onDelete={handleDelete}
        onToggleStatus={handleToggleStatus}
        onSaveEdit={handleEditSubmit}
        onCancelEdit={() => setEditingId(null)}
        onChange={handleChange}
      />

      {/* Pagination */}
      <Pagination
        currentPage={currentPage}
        totalPages={totalPages}
        totalItems={totalMembers}
        itemsPerPage={itemsPerPage}
        onPageChange={setPage}
      />
    </div>
  );
};

export default AdminMembersPage;
