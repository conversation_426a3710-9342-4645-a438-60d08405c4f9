import { useState, useEffect } from 'react';
import { Table, TableHeader, TableRow, TableHead, TableBody, TableCell } from '@/components/ui/table';
import { Input } from '@/components/ui/input';
import { Edit, Save, Trash, Plus } from 'lucide-react';
import { Member } from '../../services/api';
import { formatRupiah } from '../../utils/formatters';
import { toast } from '@/components/ui/use-toast';
import Loader from '../../components/Loader';
import { Loader2 } from 'lucide-react';
import { useMembersContext } from '../../context/MembersContext';
import { handleError, withRetry } from '../../utils/errorHandler';
import Pagination from '../../components/Pagination';

const AdminMembersPage = () => {
  const {
    members,
    loading,
    currentPage,
    totalPages,
    itemsPerPage,
    totalMembers,
    addMember,
    updateMember,
    deleteMember,
    toggleMemberStatus,
    setPage,
    searchMembers,
    filterMembers
  } = useMembersContext();
  const [editingId, setEditingId] = useState<string | null>(null);
  const [editValues, setEditValues] = useState<any>({});
  const [isAdding, setIsAdding] = useState(false);
  const [newMember, setNewMember] = useState<any>({
    name: '',
    payment_status: 'unpaid',
    payment_amount: 0,
    payment_date: null,
  });
  const [toggleLoading, setToggleLoading] = useState<string | null>(null);
  const [actionLoading, setActionLoading] = useState(false);

  // Tidak perlu useEffect karena data sudah ada di context

  const handleEdit = (member: any) => {
    setEditingId(member.id);
    setEditValues({ ...member });
  };

  const handleChange = (id: string, field: string, value: any) => {
    setEditValues(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleDelete = async (id: string) => {
    try {
      setActionLoading(true);
      await deleteMember(id);
      toast({
        title: "Sukses",
        description: "Data anggota berhasil dihapus",
      });
    } catch (error) {
      handleError(error, 'AdminMembersPage (admin)');
      toast({
        title: "Error",
        description: "Gagal menghapus data anggota",
        variant: "destructive",
      });
    } finally {
      setActionLoading(false);
    }
  };

  const handleAdd = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      setActionLoading(true);

      const newMemberData = {
        name: newMember.name,
        payment_status: newMember.payment_status,
        payment_amount: newMember.payment_amount,
        payment_date: new Date().toISOString().split('T')[0]
      };

      // Use retry mechanism for better reliability
      await withRetry(
        () => addMember(newMemberData),
        3,
        1000,
        'Add Member'
      );

      setNewMember({
        name: '',
        payment_status: 'unpaid',
        payment_amount: 0,
        payment_date: null,
      });
      setIsAdding(false);

      toast({
        title: "✅ Sukses",
        description: "Anggota berhasil ditambahkan ke database",
      });
    } catch (error) {
      handleError(error, 'Add Member');
    } finally {
      setActionLoading(false);
    }
  };



  const formatToRupiah = (number: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(number);
  };

  const parseRupiahToNumber = (rupiahString: string) => {
    return Number(rupiahString.replace(/[^0-9]/g, ''));
  };

  const handleNewMemberChange = (field: any, value: string | number | 'paid' | 'unpaid' | null) => {
    if (field === 'payment_amount') {
      const numericValue = typeof value === 'string' ? parseRupiahToNumber(value) : Number(value || 0);
      if (numericValue < 0) return;
      setNewMember({
        ...newMember,
        [field]: numericValue,
      });
    } else {
      setNewMember({
        ...newMember,
        [field]: value,
      });
    }
  };

  const handleToggleStatus = async (member: any) => {
    try {
      setToggleLoading(member.id);
      await toggleMemberStatus(member.id);
      toast({
        title: "Sukses",
        description: "Status pembayaran berhasil diperbarui di database",
      });
    } catch (error) {
      handleError(error, 'AdminMembersPage (admin)');
      toast({
        title: "Error",
        description: "Gagal memperbarui status pembayaran di database",
        variant: "destructive",
      });
    } finally {
      setToggleLoading(null);
    }
  };

  const handleEditSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      if (!editingId) return;

      setActionLoading(true);
      await updateMember(editingId, editValues);
      setEditingId(null);
      toast({
        title: "Sukses",
        description: "Data anggota berhasil diperbarui di database",
      });
    } catch (error) {
      handleError(error, 'AdminMembersPage (admin)');
      toast({
        title: "Error",
        description: "Gagal memperbarui data anggota di database",
        variant: "destructive",
      });
    } finally {
      setActionLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <Loader size="medium" variant="secondary" text="Memuat Data Anggota dari Database..." />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-black">Kelola Anggota</h2>
        <button
          type="button"
          className="neo-button-green text-sm flex items-center"
          onClick={() => setIsAdding(true)}
        >
          <Plus size={16} className="mr-2" />
          Tambah Anggota
        </button>
      </div>

      {/* Search and Filter */}
      <div className="flex flex-col sm:flex-row gap-4 items-center justify-between bg-white p-4 rounded-lg border-2 border-[#5D534B]">
        <div className="flex flex-col sm:flex-row gap-2 w-full sm:w-auto">
          <input
            type="text"
            placeholder="🔍 Cari nama anggota..."
            onChange={(e) => searchMembers(e.target.value)}
            className="px-4 py-3 sm:py-2 border-2 border-[#5D534B] rounded-lg focus:outline-none focus:ring-2 focus:ring-[#9DE0D2] text-base sm:text-sm w-full sm:w-64"
          />
          <select
            onChange={(e) => filterMembers(e.target.value as 'all' | 'paid' | 'unpaid')}
            className="px-4 py-3 sm:py-2 border-2 border-[#5D534B] rounded-lg focus:outline-none focus:ring-2 focus:ring-[#9DE0D2] text-base sm:text-sm"
            aria-label="Filter status pembayaran"
          >
            <option value="all">📋 Semua Status</option>
            <option value="paid">✅ Lunas</option>
            <option value="unpaid">❌ Belum Lunas</option>
          </select>
        </div>

        <div className="text-sm text-[#5D534B] font-medium bg-[#9DE0D2] px-3 py-2 rounded-lg border-2 border-black">
          Total: {totalMembers} anggota
        </div>
      </div>

      {/* Add Member Form */}
      {isAdding && (
        <div className="bg-white p-6 rounded-lg border-2 border-[#5D534B]">
          <h3 className="text-lg sm:text-xl font-bold mb-4">Tambah Anggota Baru</h3>
          <div className="space-y-4 sm:space-y-0 sm:grid sm:grid-cols-3 sm:gap-4 mb-6">
            <div>
              <label className="block text-sm font-medium mb-2">Nama</label>
              <Input
                value={newMember.name}
                onChange={(e) => handleNewMemberChange('name', e.target.value)}
                className="neo-input text-base sm:text-sm h-12 sm:h-10"
                required
                placeholder="Masukkan nama anggota"
              />
            </div>
            <div>
              <label htmlFor="new-member-status" className="block text-sm font-medium mb-1">Status</label>
              <select
                id="new-member-status"
                value={newMember.payment_status}
                onChange={(e) => handleNewMemberChange('payment_status', e.target.value as 'paid' | 'unpaid')}
                className="neo-input text-sm w-full"
                aria-label="Status pembayaran"
                required
              >
                <option value="unpaid">Belum Lunas</option>
                <option value="paid">Lunas</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">Jumlah</label>
              <Input
                type="text"
                inputMode="numeric"
                value={formatToRupiah(newMember.payment_amount as number)}
                onChange={(e) => handleNewMemberChange('payment_amount', e.target.value)}
                className="neo-input text-sm"
                required
                placeholder="Rp 0"
                onKeyPress={(e) => {
                  if (!/[0-9]/.test(e.key) && e.key !== 'Backspace' && e.key !== 'Delete') {
                    e.preventDefault();
                  }
                }}
              />
            </div>
          </div>
          <div className="flex justify-end space-x-2">
            <button
              type="button"
              className="px-4 py-2 bg-[#9DE0D2] border-2 border-[#5D534B] rounded-lg font-medium"
              onClick={handleAdd}
              disabled={!newMember.name || newMember.payment_amount <= 0 || actionLoading}
            >
              {actionLoading ? 'Menyimpan...' : 'Simpan'}
            </button>
            <button
              type="button"
              className="px-4 py-2 bg-[#FF9898] border-2 border-[#5D534B] rounded-lg font-medium"
              onClick={() => setIsAdding(false)}
            >
              Batal
            </button>
          </div>
        </div>
      )}

      {/* Mobile Card View */}
      <div className="block md:hidden space-y-4">
        {members.map((member: any) => (
          <div key={member.id} className="bg-white border-4 border-[#5D534B] rounded-xl shadow-[6px_6px_0px_#5D534B] p-4">
            {editingId === member.id ? (
              /* EDIT MODE MOBILE */
              <div className="space-y-4">
                <div>
                  <label htmlFor={`edit-name-${member.id}`} className="block text-sm font-medium mb-2 text-[#5D534B]">Nama:</label>
                  <input
                    id={`edit-name-${member.id}`}
                    type="text"
                    value={editValues.name || ''}
                    onChange={(e) => handleChange(member.id, 'name', e.target.value)}
                    className="w-full px-3 py-2 border-2 border-[#5D534B] rounded-lg focus:outline-none focus:ring-2 focus:ring-[#9DE0D2]"
                    placeholder="Masukkan nama anggota"
                    aria-label="Edit nama anggota"
                  />
                </div>

                <div>
                  <label htmlFor={`edit-status-${member.id}`} className="block text-sm font-medium mb-2 text-[#5D534B]">Status:</label>
                  <select
                    id={`edit-status-${member.id}`}
                    value={editValues.payment_status || 'unpaid'}
                    onChange={(e) => handleChange(member.id, 'payment_status', e.target.value)}
                    className="w-full px-3 py-2 border-2 border-[#5D534B] rounded-lg focus:outline-none focus:ring-2 focus:ring-[#9DE0D2]"
                    aria-label="Edit status pembayaran"
                    title="Pilih status pembayaran"
                  >
                    <option value="paid">Lunas</option>
                    <option value="unpaid">Belum Lunas</option>
                  </select>
                </div>

                <div>
                  <label htmlFor={`edit-amount-${member.id}`} className="block text-sm font-medium mb-2 text-[#5D534B]">Jumlah:</label>
                  <input
                    id={`edit-amount-${member.id}`}
                    type="number"
                    value={editValues.payment_amount || 0}
                    onChange={(e) => handleChange(member.id, 'payment_amount', Number(e.target.value))}
                    className="w-full px-3 py-2 border-2 border-[#5D534B] rounded-lg focus:outline-none focus:ring-2 focus:ring-[#9DE0D2]"
                    placeholder="Masukkan jumlah pembayaran"
                    aria-label="Edit jumlah pembayaran"
                    title="Masukkan jumlah pembayaran dalam rupiah"
                  />
                </div>

                <div>
                  <label htmlFor={`edit-date-${member.id}`} className="block text-sm font-medium mb-2 text-[#5D534B]">Tanggal:</label>
                  <input
                    id={`edit-date-${member.id}`}
                    type="date"
                    value={editValues.payment_date || ''}
                    onChange={(e) => handleChange(member.id, 'payment_date', e.target.value)}
                    className="w-full px-3 py-2 border-2 border-[#5D534B] rounded-lg focus:outline-none focus:ring-2 focus:ring-[#9DE0D2]"
                    aria-label="Edit tanggal pembayaran"
                    title="Pilih tanggal pembayaran"
                  />
                </div>

                <div className="flex space-x-2 pt-2">
                  <button
                    type="button"
                    className="flex-1 px-3 py-2 bg-[#9DE0D2] border-2 border-[#5D534B] text-sm font-medium text-[#5D534B] rounded-lg"
                    onClick={handleEditSubmit}
                  >
                    ✅ Simpan
                  </button>
                  <button
                    type="button"
                    className="flex-1 px-3 py-2 bg-[#FF9898] border-2 border-[#5D534B] text-sm font-medium text-[#5D534B] rounded-lg"
                    onClick={() => setEditingId(null)}
                  >
                    ❌ Batal
                  </button>
                </div>
              </div>
            ) : (
              /* VIEW MODE MOBILE */
              <>
                <div className="flex justify-between items-start mb-3">
                  <h3 className="font-bold text-lg text-[#5D534B]">{member.name}</h3>
                  <button
                    type="button"
                    onClick={() => handleToggleStatus(member)}
                    disabled={toggleLoading === member.id}
                    className={`px-3 py-1 rounded-full text-sm font-medium border-2 border-[#5D534B] ${
                      member.payment_status === 'paid'
                        ? 'bg-[#9DE0D2] text-[#5D534B]'
                        : 'bg-[#FF9898] text-[#5D534B]'
                    }`}
                  >
                    {toggleLoading === member.id ? (
                      <Loader2 className="h-4 w-4 animate-spin inline mr-1" />
                    ) : null}
                    {member.payment_status === 'paid' ? 'Lunas' : 'Belum'}
                  </button>
                </div>

                <div className="space-y-2 mb-4">
                  <div className="flex justify-between">
                    <span className="text-sm text-[#5D534B]">Jumlah:</span>
                    <span className="font-bold text-[#5D534B]">{formatRupiah(member.payment_amount)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-[#5D534B]">Tanggal:</span>
                    <span className="text-sm text-[#5D534B]">{member.payment_date || '-'}</span>
                  </div>
                </div>

                <div className="flex space-x-2">
                  <button
                    type="button"
                    className="flex-1 px-3 py-2 bg-[#9DE0D2] border-2 border-[#5D534B] text-sm font-medium text-[#5D534B] rounded-lg"
                    onClick={() => handleEdit(member)}
                  >
                    ✏️ Edit
                  </button>
                  <button
                    type="button"
                    className="flex-1 px-3 py-2 bg-[#FF9898] border-2 border-[#5D534B] text-sm font-medium text-[#5D534B] rounded-lg"
                    onClick={() => handleDelete(member.id)}
                  >
                    🗑️ Hapus
                  </button>
                </div>
              </>
            )}
          </div>
        ))}
      </div>

      {/* Desktop Table View */}
      <div className="hidden md:block overflow-x-auto">
        <Table className="neo-table min-w-full">
          <TableHeader>
            <TableRow>
              <TableHead className="text-sm whitespace-nowrap">Nama</TableHead>
              <TableHead className="text-sm whitespace-nowrap">Status</TableHead>
              <TableHead className="text-sm whitespace-nowrap">Jumlah</TableHead>
              <TableHead className="text-sm whitespace-nowrap">Tanggal</TableHead>
              <TableHead className="text-sm text-right whitespace-nowrap">Aksi</TableHead>
            </TableRow>
          </TableHeader>
        <TableBody>
          {members.map((member: any) => (
            <TableRow key={member.id}>
              <TableCell className="text-sm">
                {editingId === member.id ? (
                  <Input
                    value={editValues.name || ''}
                    onChange={(e) => handleChange(member.id, 'name', e.target.value)}
                    className="neo-input text-sm"
                  />
                ) : (
                  member.name
                )}
              </TableCell>
              <TableCell className="text-sm">
                {editingId === member.id ? (
                  <select
                    id={`edit-status-${member.id}`}
                    value={editValues.payment_status || 'unpaid'}
                    onChange={(e) => handleChange(member.id, 'payment_status', e.target.value as 'paid' | 'unpaid')}
                    className="neo-input text-sm"
                    aria-label="Edit status pembayaran"
                  >
                    <option value="paid">Lunas</option>
                    <option value="unpaid">Belum Lunas</option>
                  </select>
                ) : (
                  <button
                    type="button"
                    onClick={() => handleToggleStatus(member)}
                    disabled={toggleLoading === member.id}
                    className={`px-2.5 py-1 rounded-full text-xs sm:text-sm font-medium cursor-pointer transition-all duration-150 min-w-[70px] text-center inline-flex items-center justify-center border-2 border-[#5D534B] bg-white shadow-[3px_3px_0px_rgba(93,83,75,0.7)] active:shadow-[1px_1px_0px_rgba(93,83,75,0.7)] active:translate-x-[2px] active:translate-y-[2px] ${
                      member.payment_status === 'paid'
                        ? 'text-green-600 hover:bg-green-50'
                        : 'text-red-600 hover:bg-red-50'
                    }`}
                  >
                    {toggleLoading === member.id ? (
                      <Loader2 className="h-4 w-4 animate-spin inline mr-1" />
                    ) : null}
                    {member.payment_status === 'paid' ? 'Lunas' : 'Belum'}
                  </button>
                )}
              </TableCell>
              <TableCell className="text-sm">
                {editingId === member.id ? (
                  <Input
                    type="number"
                    value={editValues.payment_amount || 0}
                    onChange={(e) => handleChange(member.id, 'payment_amount', Number(e.target.value))}
                    className="neo-input text-sm"
                  />
                ) : (
                  formatRupiah(member.payment_amount)
                )}
              </TableCell>
              <TableCell className="text-sm">
                {editingId === member.id ? (
                  <Input
                    type="date"
                    value={editValues.payment_date || ''}
                    onChange={(e) => handleChange(member.id, 'payment_date', e.target.value)}
                    className="neo-input text-sm"
                  />
                ) : (
                  member.payment_date || '-'
                )}
              </TableCell>
              <TableCell className="text-right space-x-2">
                {editingId === member.id ? (
                  <button
                    type="button"
                    className="px-2 py-1 bg-neo-green border-2 border-black text-sm inline-flex items-center"
                    onClick={handleEditSubmit}
                  >
                    <Save size={14} className="mr-1" />
                    Simpan
                  </button>
                ) : (
                  <button
                    type="button"
                    className="px-2 py-1 bg-neo-blue border-2 border-black text-sm inline-flex items-center"
                    onClick={() => handleEdit(member)}
                  >
                    <Edit size={14} className="mr-1" />
                    Edit
                  </button>
                )}
                <button
                  type="button"
                  className="px-2 py-1 bg-neo-red text-white border-2 border-black text-sm inline-flex items-center"
                  onClick={() => handleDelete(member.id)}
                >
                  <Trash size={14} className="mr-1" />
                  Hapus
                </button>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      <Pagination
        currentPage={currentPage}
        totalPages={totalPages}
        totalItems={totalMembers}
        itemsPerPage={itemsPerPage}
        onPageChange={setPage}
      />
    </div>
  );
};

export default AdminMembersPage;
