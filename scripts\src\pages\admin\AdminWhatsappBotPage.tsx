import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { whatsappBotAPI, WhatsAppBotConfig, WhatsAppMessageTemplate, WhatsAppNotificationLog, Group } from '@/services/api';
import { toast } from '@/components/ui/use-toast';
import { LoaderCircle, RefreshCw, Send, MessageSquare } from 'lucide-react';
import { formatDate, formatTime } from '@/utils/formatters';
import { useAuth } from '../../context/AuthContext';

// Helper function for formatting datetime
const formatDatetime = (dateString: string): string => {
  if (!dateString) return '-';
  return `${formatDate(dateString)} ${formatTime(dateString)}`;
};

const AdminWhatsappBotPage = () => {
  const { user } = useAuth();
  const [botConfig, setBotConfig] = useState<WhatsAppBotConfig | null>(null);
  const [templates, setTemplates] = useState<WhatsAppMessageTemplate[]>([]);
  const [logs, setLogs] = useState<WhatsAppNotificationLog[]>([]);
  const [botStatus, setBotStatus] = useState<{ status: string; isConnected: boolean } | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [isCheckingStatus, setIsCheckingStatus] = useState(false);
  const [qrCode, setQrCode] = useState<string>('');
  const [groups, setGroups] = useState<Group[]>([]);

  // State untuk fitur pengiriman pesan
  const [selectedTemplate, setSelectedTemplate] = useState<string>('');
  const [customMessage, setCustomMessage] = useState<string>('');
  const [isSendingMessage, setIsSendingMessage] = useState(false);

  // Tambahkan state yang hilang
  const [editingTemplate, setEditingTemplate] = useState<WhatsAppMessageTemplate | null>(null);
  const [isAddTemplateModalOpen, setIsAddTemplateModalOpen] = useState(false);

  // Fetch data
  const fetchData = async () => {
    setIsLoading(true);
    try {
      const [configData, templatesData, logsData] = await Promise.all([
        whatsappBotAPI.getConfig(),
        whatsappBotAPI.getTemplates(),
        whatsappBotAPI.getLogs(50),
      ]);

      setBotConfig(configData);
      setTemplates(templatesData);
      setLogs(logsData);

      if (configData.is_active) {
        checkBotStatus(configData);
      }
    } catch (error) {
      console.error('Error fetching WhatsApp bot data:', error);
      toast({
        title: 'Error',
        description: 'Gagal memuat data WhatsApp Bot',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const checkBotStatus = async (config: WhatsAppBotConfig | null) => {
    if (!config) return;

    setIsCheckingStatus(true);
    try {
      const status = await whatsappBotAPI.checkBotStatus(config);
      setBotStatus(status);

      if (status.isConnected) {
        // Jika terhubung, ambil daftar grup
        const groups = await whatsappBotAPI.getGroups(config);
        setGroups(groups);
      }
    } catch (error) {
      console.error('Error checking bot status:', error);
      setBotStatus({ status: 'error', isConnected: false });
    } finally {
      setIsCheckingStatus(false);
    }
  };

  // Fungsi baru untuk refresh grup secara manual
  const refreshGroups = async () => {
    if (!botConfig || !botStatus?.isConnected) return;

    try {
      const groups = await whatsappBotAPI.getGroups(botConfig);
      setGroups(groups);
      toast({
        title: 'Sukses',
        description: 'Daftar grup berhasil diperbarui',
      });
    } catch (error) {
      console.error('Error refreshing groups:', error);
      toast({
        title: 'Error',
        description: 'Gagal memperbarui daftar grup',
        variant: 'destructive',
      });
    }
  };

  // Toggle bot active state
  const toggleBotActive = async () => {
    if (!botConfig) return;

    setIsSaving(true);
    try {
      const updatedConfig = await whatsappBotAPI.toggleActive(botConfig.id, !botConfig.is_active);
      setBotConfig(updatedConfig);

      toast({
        title: 'Sukses',
        description: `WhatsApp Bot berhasil ${updatedConfig.is_active ? 'diaktifkan' : 'dinonaktifkan'}`,
      });

      // Jika diaktifkan, periksa status
      if (updatedConfig.is_active) {
        checkBotStatus(updatedConfig);
      } else {
        setBotStatus(null);
      }
    } catch (error) {
      console.error('Error toggling bot active state:', error);
      toast({
        title: 'Error',
        description: 'Gagal mengubah status bot',
        variant: 'destructive',
      });
    } finally {
      setIsSaving(false);
    }
  };

  // Update bot config
  const updateBotConfig = async () => {
    if (!botConfig) return;

    setIsSaving(true);
    try {
      const updatedConfig = await whatsappBotAPI.updateConfig(botConfig);
      setBotConfig(updatedConfig);

      toast({
        title: 'Sukses',
        description: 'Konfigurasi WhatsApp Bot berhasil disimpan',
      });
    } catch (error) {
      console.error('Error updating bot config:', error);
      toast({
        title: 'Error',
        description: 'Gagal menyimpan konfigurasi bot',
        variant: 'destructive',
      });
    } finally {
      setIsSaving(false);
    }
  };

  // Update message template
  const updateTemplate = async (template: WhatsAppMessageTemplate) => {
    setIsSaving(true);
    try {
      await whatsappBotAPI.updateTemplate(template.id, template.template);

      // Update templates in state
      setTemplates(templates.map(t =>
        t.id === template.id ? { ...t, template: template.template } : t
      ));

      toast({
        title: 'Sukses',
        description: 'Template pesan berhasil disimpan',
      });
    } catch (error) {
      console.error('Error updating template:', error);
      toast({
        title: 'Error',
        description: 'Gagal menyimpan template pesan',
        variant: 'destructive',
      });
    } finally {
      setIsSaving(false);
    }
  };

  // Fungsi untuk mengirim pesan dengan template
  const sendMessage = async () => {
    if (!botConfig || !botConfig.group_id) {
      toast({
        title: 'Error',
        description: 'Pilih grup WhatsApp terlebih dahulu',
        variant: 'destructive',
      });
      return;
    }

    let messageText = customMessage;

    // Jika template dipilih, gunakan template
    if (selectedTemplate) {
      const template = templates.find(t => t.id === selectedTemplate);
      if (template) {
        messageText = template.template;
      }
    }

    if (!messageText) {
      toast({
        title: 'Error',
        description: 'Pesan tidak boleh kosong',
        variant: 'destructive',
      });
      return;
    }

    setIsSendingMessage(true);
    try {
      const result = await whatsappBotAPI.sendTextMessage(botConfig, messageText);

      if (result.status === 'success') {
        toast({
          title: 'Sukses',
          description: 'Pesan berhasil dikirim',
        });

        // Catat pesan ke log
        await whatsappBotAPI.createLog({
          event_type: selectedTemplate ? 'template' : 'custom',
          recipient: botConfig.group_id,
          message: messageText,
          status: 'sent'
        });

        // Reset custom message
        setCustomMessage('');
      } else {
        throw new Error('Gagal mengirim pesan');
      }
    } catch (error) {
      console.error('Error sending message:', error);
      toast({
        title: 'Error',
        description: 'Gagal mengirim pesan',
        variant: 'destructive',
      });
    } finally {
      setIsSendingMessage(false);
    }
  };

  // Render status badge
  const renderStatusBadge = (status: string, isConnected: boolean) => {
    if (status === 'connected' && isConnected) {
      return <Badge className="bg-green-500">Terhubung</Badge>;
    } else if (status === 'connecting') {
      return <Badge className="bg-yellow-500">Menghubungkan...</Badge>;
    } else if (status === 'require_scan') {
      return <Badge className="bg-blue-500">Butuh Scan QR</Badge>;
    } else {
      return <Badge className="bg-red-500">Tidak Terhubung</Badge>;
    }
  };

  // Render log status badge
  const renderLogStatusBadge = (status: string) => {
    if (status === 'sent') {
      return <Badge className="bg-green-500">Terkirim</Badge>;
    } else if (status === 'pending') {
      return <Badge className="bg-yellow-500">Pending</Badge>;
    } else {
      return <Badge className="bg-red-500">Gagal</Badge>;
    }
  };

  // Handle template change
  const handleTemplateChange = (id: string, value: string) => {
    setTemplates(templates.map(t =>
      t.id === id ? { ...t, template: value } : t
    ));
  };

  // Handle config change
  const handleConfigChange = (field: keyof WhatsAppBotConfig, value: string) => {
    if (!botConfig) return;
    setBotConfig({ ...botConfig, [field]: value });
  };

  // Handle group selection & save to database
  const handleGroupSelect = async (group: Group) => {
    if (!botConfig) return;

    try {
      // Update state lokal dulu
      setBotConfig({
        ...botConfig,
        group_id: group.id
      });

      // Simpan ke database juga
      const updatedConfig = await whatsappBotAPI.updateConfig({
        ...botConfig,
        group_id: group.id
      });

      setBotConfig(updatedConfig);

      toast({
        title: 'Sukses',
        description: `Grup "${group.name}" dipilih sebagai tujuan pesan`,
      });
    } catch (error) {
      console.error('Error saving group selection:', error);
      toast({
        title: 'Error',
        description: 'Gagal menyimpan pilihan grup',
        variant: 'destructive',
      });
    }
  };

  // Tambahkan fungsi yang hilang
  const handleDeleteTemplate = async (id: string) => {
    if (confirm('Apakah Anda yakin ingin menghapus template ini?')) {
      try {
        // Implementasi hapus template bisa ditambahkan di API
        // await whatsappBotAPI.deleteTemplate(id);

        // Update state
        setTemplates(templates.filter(t => t.id !== id));

        toast({
          title: 'Sukses',
          description: 'Template berhasil dihapus',
        });
      } catch (error) {
        console.error('Error deleting template:', error);
        toast({
          title: 'Error',
          description: 'Gagal menghapus template',
          variant: 'destructive',
        });
      }
    }
  };

  // Initial data load
  useEffect(() => {
    fetchData();
  }, []);

  // Cek status bot setiap 5 detik jika aktif
  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (botConfig?.is_active) {
      checkBotStatus(botConfig);
      // Kurangi frekuensi polling untuk mencegah rate-limit
      interval = setInterval(() => checkBotStatus(botConfig), 10000); // Ubah dari 5000 menjadi 10000 ms
    }
    return () => clearInterval(interval);
  }, [botConfig?.is_active]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-full">
        <LoaderCircle className="w-6 h-6 text-primary animate-spin" />
        <span className="ml-2">Loading...</span>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl font-bold mb-6">WhatsApp Bot Configuration</h1>

      {/* Status Bot */}
      <div className="bg-white rounded-lg shadow-md p-6 mb-6">
        <h2 className="text-xl font-semibold mb-4">Status Bot</h2>
        <div className="flex items-center space-x-4">
          <div className={`w-4 h-4 rounded-full ${botStatus?.isConnected ? 'bg-green-500' : 'bg-red-500'}`} />
          <span className="text-lg">
            {botStatus?.isConnected ? 'Terhubung' : 'Tidak Terhubung'} - {botStatus?.status}
          </span>
        </div>
      </div>

      {/* Tabs untuk navigasi */}
      <Tabs defaultValue="konfigurasi" className="mb-6">
        <TabsList className="mb-4">
          <TabsTrigger value="konfigurasi">Konfigurasi</TabsTrigger>
          <TabsTrigger value="grup" disabled={!botStatus?.isConnected}>Grup WhatsApp</TabsTrigger>
          <TabsTrigger value="kirim-pesan" disabled={!botStatus?.isConnected || !botConfig?.group_id}>Kirim Pesan</TabsTrigger>
          <TabsTrigger value="template">Template Pesan</TabsTrigger>
          <TabsTrigger value="log">Log Notifikasi</TabsTrigger>
        </TabsList>

        {/* Tab Konfigurasi */}
        <TabsContent value="konfigurasi">
          <div className="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 className="text-xl font-semibold mb-4">Konfigurasi Bot</h2>
            <div className="space-y-4">
              <div>
                <label htmlFor="session_id" className="block text-sm font-medium text-gray-700">Session ID</label>
                <input
                  id="session_id"
                  type="text"
                  value={botConfig?.session_id || ''}
                  onChange={(e) => handleConfigChange('session_id', e.target.value)}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                  placeholder="Masukkan Session ID WhatsApp"
                  title="Session ID WhatsApp"
                />
              </div>
              <div>
                <label htmlFor="api_key" className="block text-sm font-medium text-gray-700">API Key</label>
                <input
                  id="api_key"
                  type="password"
                  value={botConfig?.api_key || ''}
                  onChange={(e) => handleConfigChange('api_key', e.target.value)}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                  placeholder="Masukkan API Key WhatsApp"
                  title="API Key WhatsApp"
                />
              </div>
              <div className="flex items-center space-x-4">
                <button
                  onClick={updateBotConfig}
                  disabled={isSaving}
                  className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 disabled:opacity-50"
                >
                  {isSaving ? 'Menyimpan...' : 'Simpan Konfigurasi'}
                </button>
                <button
                  onClick={toggleBotActive}
                  disabled={isSaving}
                  className={`${
                    botConfig?.is_active ? 'bg-red-500 hover:bg-red-600' : 'bg-green-500 hover:bg-green-600'
                  } text-white px-4 py-2 rounded disabled:opacity-50`}
                >
                  {isSaving ? 'Memproses...' : botConfig?.is_active ? 'Nonaktifkan Bot' : 'Aktifkan Bot'}
                </button>
              </div>
            </div>
          </div>

          {/* QR Code Scanner */}
          {!botStatus?.isConnected && (
            <div className="bg-white rounded-lg shadow-md p-6 mb-6">
              <h2 className="text-xl font-semibold mb-4">Scan QR Code</h2>
              {qrCode ? (
                <img src={qrCode} alt="QR Code" className="mx-auto" />
              ) : (
                <button
                  onClick={() => {
                    if (botConfig) {
                      whatsappBotAPI.getQRCode(botConfig).then(code => {
                        setQrCode(code);
                      });
                    }
                  }}
                  className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
                >
                  Tampilkan QR Code
                </button>
              )}
            </div>
          )}
        </TabsContent>

        {/* Tab Grup WhatsApp */}
        <TabsContent value="grup">
          <div className="bg-white rounded-lg shadow-md p-6 mb-6">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold">Grup WhatsApp</h2>
              <button
                onClick={refreshGroups}
                className="flex items-center gap-2 bg-blue-500 text-white px-3 py-2 rounded hover:bg-blue-600"
              >
                <RefreshCw className="w-4 h-4" />
                Refresh Grup
              </button>
            </div>

            {groups.length > 0 ? (
              <div className="space-y-4">
                {groups.map((group) => (
                  <div
                    key={group.id}
                    className={`p-4 border rounded-lg cursor-pointer ${
                      botConfig?.group_id === group.id ? 'border-blue-500 bg-blue-50' : 'border-gray-200'
                    }`}
                    onClick={() => handleGroupSelect(group)}
                  >
                    <div className="flex justify-between items-center">
                      <h3 className="font-medium">{group.name}</h3>
                      {botConfig?.group_id === group.id && (
                        <Badge className="bg-green-500">Aktif</Badge>
                      )}
                    </div>
                    <p className="text-sm text-gray-500 mt-2">
                      {Array.isArray(group.participants)
                        ? `${group.participants.length} peserta`
                        : `${group.participants} peserta`}
                    </p>
                    <p className="text-xs text-gray-400 mt-1">ID: {group.id}</p>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                <p>Tidak ada grup yang ditemukan</p>
                <p className="text-sm mt-2">Pastikan Anda telah terhubung dan menjadi admin grup</p>
              </div>
            )}
          </div>
        </TabsContent>

        {/* Tab Kirim Pesan */}
        <TabsContent value="kirim-pesan">
          <div className="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 className="text-xl font-semibold mb-4">Kirim Pesan ke Grup</h2>

            {botConfig?.group_id ? (
              <>
                <div className="bg-blue-50 p-3 rounded mb-4">
                  <p className="text-blue-700">
                    <strong>Grup Tujuan:</strong> {groups.find(g => g.id === botConfig.group_id)?.name || 'Grup Terpilih'}
                  </p>
                </div>

                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Template Pesan (Opsional)</label>
                    <select
                      value={selectedTemplate}
                      onChange={(e) => setSelectedTemplate(e.target.value)}
                      className="w-full p-2 border border-gray-300 rounded-md"
                      aria-label="Pilih template pesan"
                    >
                      <option value="">-- Pilih Template --</option>
                      {templates.map(t => (
                        <option key={t.id} value={t.id}>
                          {t.type === 'new_member' ? 'Template Anggota Baru' :
                           t.type === 'edit_member' ? 'Template Edit Anggota' :
                           t.type === 'new_expense' ? 'Template Pengeluaran Baru' : t.type}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div className="mt-4">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Template Preview:
                    </label>
                    {selectedTemplate && (
                      <p className="p-4 bg-gray-100 rounded-lg whitespace-pre-wrap">
                        {String(templates.find(t => t.id === selectedTemplate)?.template || '')}
                      </p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Pesan Kustom</label>
                      <Textarea
                        value={customMessage}
                        onChange={(e) => setCustomMessage(e.target.value)}
                        placeholder="Ketik pesan yang ingin dikirim..."
                        className="min-h-[100px]"
                      />
                  </div>

                  <div>
                    <button
                      onClick={sendMessage}
                      disabled={isSendingMessage || (!selectedTemplate && !customMessage)}
                      className="flex items-center gap-2 bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 disabled:opacity-50"
                    >
                      {isSendingMessage ? <LoaderCircle className="w-4 h-4 animate-spin" /> : <Send className="w-4 h-4" />}
                      {isSendingMessage ? 'Mengirim Pesan...' : 'Kirim Pesan'}
                    </button>
                  </div>
                </div>
              </>
            ) : (
              <div className="text-center py-8 text-gray-500">
                <MessageSquare className="w-12 h-12 mx-auto text-gray-300 mb-2" />
                <p>Belum ada grup terpilih</p>
                <p className="text-sm mt-2">Pilih grup terlebih dahulu di tab "Grup WhatsApp"</p>
              </div>
            )}
          </div>
        </TabsContent>

        {/* Tab Template Pesan */}
        <TabsContent value="template">
          <div className="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 className="text-xl font-semibold mb-4">Template Pesan</h2>
            <div className="space-y-6">
              {templates.map((template) => (
                <div
                  key={template.id}
                  className="bg-white p-4 rounded border mb-4"
                >
                  <div className="flex justify-between items-center mb-2">
                    <h3 className="text-md font-medium">
                      {template.type === 'new_member' ? 'Template Anggota Baru' :
                       template.type === 'edit_member' ? 'Template Edit Anggota' :
                       template.type === 'new_expense' ? 'Template Pengeluaran Baru' : template.type}
                    </h3>
                    <div className="flex space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          setEditingTemplate(template);
                          setIsAddTemplateModalOpen(true);
                        }}
                      >
                        Edit
                      </Button>
                      <Button
                        variant="destructive"
                        size="sm"
                        onClick={() => handleDeleteTemplate(template.id)}
                      >
                        Hapus
                      </Button>
                    </div>
                  </div>
                  <p className="text-gray-600 whitespace-pre-wrap">{String(template.template)}</p>
                </div>
              ))}
            </div>
          </div>
        </TabsContent>

        {/* Tab Log Notifikasi */}
        <TabsContent value="log">
          <div className="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 className="text-xl font-semibold mb-4">Log Notifikasi</h2>
            {logs.length > 0 ? (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Waktu
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Tipe Event
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Penerima
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Pesan
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {logs.map((log) => (
                      <tr key={log.id}>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {formatDatetime(log.created_at || '')}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {log.event_type}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 truncate max-w-[150px]">
                          {log.recipient}
                        </td>
                        <td className="px-6 py-4 text-sm text-gray-500 truncate max-w-[300px]">
                          {log.message}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {renderLogStatusBadge(log.status)}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                <p>Belum ada log notifikasi</p>
              </div>
            )}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default AdminWhatsappBotPage;