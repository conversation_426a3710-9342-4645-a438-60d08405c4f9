import React, { createContext, useContext, useState, useEffect, ReactNode, useMemo } from 'react';
import {
  collection,
  addDoc,
  updateDoc,
  deleteDoc,
  doc,
  onSnapshot,
  serverTimestamp,
  query,
  orderBy,
  limit,
  Timestamp
} from 'firebase/firestore';
import { db } from '../lib/firebase';

interface ExpenseData {
  id: string;
  description: string;
  amount: number;
  date: string;
  category?: string;
  type: 'expense';
  createdBy: string;
  createdAt?: Timestamp;
  updatedAt?: Timestamp;
}

interface ExpensesContextType {
  expenses: ExpenseData[];
  loading: boolean;
  addExpense: (expense: Omit<ExpenseData, 'id'>) => Promise<string>;
  updateExpense: (id: string, updates: Partial<ExpenseData>) => Promise<void>;
  deleteExpense: (id: string) => Promise<void>;
  getTotalExpenses: () => number;
}

const ExpensesContext = createContext<ExpensesContextType | undefined>(undefined);

export const useExpensesContext = () => {
  const context = useContext(ExpensesContext);
  if (!context) {
    throw new Error('useExpensesContext must be used within an ExpensesProvider');
  }
  return context;
};

interface ExpensesProviderProps {
  children: ReactNode;
}

export const ExpensesProvider: React.FC<ExpensesProviderProps> = ({ children }) => {
  const [expenses, setExpenses] = useState<ExpenseData[]>([]);
  const [loading, setLoading] = useState(true);

  // Optimized collection reference with query
  const expensesQuery = useMemo(() =>
    query(
      collection(db, 'expenses'),
      orderBy('date', 'desc'),
      limit(100) // Limit to last 100 expenses for performance
    ),
  []);

  // Load data from Firestore with optimized query
  useEffect(() => {
    const unsubscribe = onSnapshot(expensesQuery, (snapshot) => {
      const expensesData: ExpenseData[] = [];
      snapshot.forEach((doc) => {
        expensesData.push({
          id: doc.id,
          ...doc.data()
        } as ExpenseData);
      });

      // Sort by date descending (newest first)
      expensesData.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());

      setExpenses(expensesData);
      setLoading(false);
    }, () => {
      setLoading(false);
    });

    return () => unsubscribe();
  }, [expensesQuery]);

  const validateExpenseData = (expenseData: Omit<ExpenseData, 'id'>) => {
    const errors: string[] = [];

    // Validasi deskripsi
    if (!expenseData.description || expenseData.description.trim().length < 3) {
      errors.push('Deskripsi harus minimal 3 karakter');
    }
    if (expenseData.description && expenseData.description.length > 100) {
      errors.push('Deskripsi maksimal 100 karakter');
    }

    // Validasi nominal
    if (!expenseData.amount || expenseData.amount <= 0) {
      errors.push('Nominal harus lebih dari 0');
    }
    if (expenseData.amount && expenseData.amount > 100000000) {
      errors.push('Nominal maksimal Rp 100.000.000');
    }

    // Validasi tanggal
    if (expenseData.date) {
      const expenseDate = new Date(expenseData.date);
      const today = new Date();
      const oneYearAgo = new Date();
      oneYearAgo.setFullYear(today.getFullYear() - 1);

      if (expenseDate > today) {
        errors.push('Tanggal pengeluaran tidak boleh di masa depan');
      }
      if (expenseDate < oneYearAgo) {
        errors.push('Tanggal pengeluaran tidak boleh lebih dari 1 tahun yang lalu');
      }
    }

    return errors;
  };

  const addExpense = async (expenseData: Omit<ExpenseData, 'id'>) => {
    try {
      // Validasi data
      const validationErrors = validateExpenseData(expenseData);
      if (validationErrors.length > 0) {
        throw new Error(`❌ VALIDASI GAGAL: ${validationErrors.join(', ')}`);
      }

      const docRef = await addDoc(collection(db, 'expenses'), {
        ...expenseData,
        description: expenseData.description.trim(), // Trim whitespace
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      });

      return docRef.id;
    } catch (error) {
      throw error;
    }
  };

  const updateExpense = async (id: string, updates: Partial<ExpenseData>) => {
    try {
      const expenseDoc = doc(db, 'expenses', id);
      await updateDoc(expenseDoc, {
        ...updates,
        updatedAt: serverTimestamp()
      });
    } catch (error) {
      throw error;
    }
  };

  const deleteExpense = async (id: string) => {
    try {
      const expenseDoc = doc(db, 'expenses', id);
      await deleteDoc(expenseDoc);
    } catch (error) {
      throw error;
    }
  };

  const getTotalExpenses = () => {
    return expenses.reduce((total, expense) => total + expense.amount, 0);
  };

  const value: ExpensesContextType = {
    expenses,
    loading,
    addExpense,
    updateExpense,
    deleteExpense,
    getTotalExpenses,
  };

  return (
    <ExpensesContext.Provider value={value}>
      {children}
    </ExpensesContext.Provider>
  );
};

export default ExpensesContext;
