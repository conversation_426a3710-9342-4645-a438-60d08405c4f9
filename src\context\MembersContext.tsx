import React, { createContext, useContext, ReactNode } from 'react';
import { useMembers, MemberData } from '../hooks/useMembers';

// Use the return type from useMembers hook
type MembersContextType = ReturnType<typeof useMembers>;

const MembersContext = createContext<MembersContextType | undefined>(undefined);

export const useMembersContext = () => {
  const context = useContext(MembersContext);
  if (!context) {
    throw new Error('useMembersContext must be used within a MembersProvider');
  }
  return context;
};

interface MembersProviderProps {
  children: ReactNode;
}

export const MembersProvider: React.FC<MembersProviderProps> = ({ children }) => {
  // Use the custom hook that contains all the logic
  const membersData = useMembers();

  console.log('🏗️ MembersProvider - members count:', membersData.allMembers.length, 'loading:', membersData.loading);

  return (
    <MembersContext.Provider value={membersData}>
      {children}
    </MembersContext.Provider>
  );
};

// Export MemberData type for use in other components
export type { MemberData };
export default MembersContext;

