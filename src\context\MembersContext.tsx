import React, { createContext, useContext, useState, useEffect, ReactNode, useMemo, useCallback } from 'react';
import {
  collection,
  addDoc,
  updateDoc,
  deleteDoc,
  doc,
  getDocs,
  onSnapshot,
  serverTimestamp
} from 'firebase/firestore';
import { db } from '../lib/firebase';

interface MemberData {
  id: string;
  name: string;
  payment_status: 'paid' | 'unpaid';
  payment_amount: number;
  payment_date: string | null;
}

interface MembersContextType {
  members: MemberData[];
  allMembers: MemberData[]; // All members for search/filter
  loading: boolean;
  currentPage: number;
  totalPages: number;
  itemsPerPage: number;
  totalMembers: number;
  addMember: (member: Omit<MemberData, 'id'>) => Promise<void>;
  updateMember: (id: string, updates: Partial<MemberData>) => Promise<void>;
  deleteMember: (id: string) => Promise<void>;
  toggleMemberStatus: (id: string) => Promise<void>;
  setPage: (page: number) => void;
  setItemsPerPage: (items: number) => void;
  searchMembers: (query: string) => void;
  filterMembers: (status: 'all' | 'paid' | 'unpaid') => void;
}

const MembersContext = createContext<MembersContextType | undefined>(undefined);

export const useMembersContext = () => {
  const context = useContext(MembersContext);
  if (!context) {
    throw new Error('useMembersContext must be used within a MembersProvider');
  }
  return context;
};

interface MembersProviderProps {
  children: ReactNode;
}

export const MembersProvider: React.FC<MembersProviderProps> = ({ children }) => {
  const [allMembers, setAllMembers] = useState<MemberData[]>([]);
  const [members, setMembers] = useState<MemberData[]>([]);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPageState] = useState(10); // Kurangi dari 20 ke 10
  const [searchQuery, setSearchQuery] = useState('');
  const [filterStatus, setFilterStatus] = useState<'all' | 'paid' | 'unpaid'>('all');

  // Collection reference with optimization
  const membersCollection = useMemo(() => collection(db, 'members'), []);

  // Load data from Firestore with Chrome compatibility
  useEffect(() => {
    let unsubscribe: (() => void) | null = null;

    const loadMembers = async () => {
      try {
        // First try to get data directly with timeout (better for Chrome)
        const timeoutPromise = new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Firebase timeout')), 10000)
        );

        const snapshot = await Promise.race([
          getDocs(membersCollection),
          timeoutPromise
        ]) as any;
        const membersData: MemberData[] = [];
        snapshot.forEach((doc: any) => {
          membersData.push({
            id: doc.id,
            ...doc.data()
          } as MemberData);
        });

        setAllMembers(membersData);
        setLoading(false);

        // Then set up real-time listener
        unsubscribe = onSnapshot(membersCollection, (snapshot) => {
          const updatedMembersData: MemberData[] = [];
          snapshot.forEach((doc) => {
            updatedMembersData.push({
              id: doc.id,
              ...doc.data()
            } as MemberData);
          });
          setAllMembers(updatedMembersData);
        }, (error) => {
          // Don't set loading to false here, keep the initial data
        });

      } catch (error) {
        setLoading(false);

        // Show user-friendly error
        if (typeof window !== 'undefined') {
          // Check if it's a permissions error
          if (error instanceof Error && error.message.includes('Missing or insufficient permissions')) {
            console.error('🔐 Firebase Rules Error: Database rules need to be configured');
            console.error('📋 Solution: Update Firestore Rules to allow read/write access');
          }
        }
      }
    };

    loadMembers();

    return () => {
      if (unsubscribe) {
        unsubscribe();
      }
    };
  }, [membersCollection]);

  // Optimized filter and paginate with useMemo
  const filteredAndPaginatedMembers = useMemo(() => {
    let filteredMembers = allMembers;

    // Apply search filter
    if (searchQuery) {
      filteredMembers = filteredMembers.filter(member =>
        member.name.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Apply status filter
    if (filterStatus !== 'all') {
      filteredMembers = filteredMembers.filter(member =>
        member.payment_status === filterStatus
      );
    }

    // Apply pagination
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const paginatedMembers = filteredMembers.slice(startIndex, endIndex);

    return {
      members: paginatedMembers,
      totalMembers: filteredMembers.length,
      totalPages: Math.ceil(filteredMembers.length / itemsPerPage)
    };
  }, [allMembers, searchQuery, filterStatus, currentPage, itemsPerPage]);

  // Update members when filtered data changes
  useEffect(() => {
    setMembers(filteredAndPaginatedMembers.members);
  }, [filteredAndPaginatedMembers.members]);

  const validateMemberData = (memberData: Omit<MemberData, 'id'>) => {
    const errors: string[] = [];

    // Validasi nama
    if (!memberData.name || memberData.name.trim().length < 2) {
      errors.push('Nama harus minimal 2 karakter');
    }
    if (memberData.name && memberData.name.length > 50) {
      errors.push('Nama maksimal 50 karakter');
    }

    // Validasi nominal
    if (!memberData.payment_amount || memberData.payment_amount <= 0) {
      errors.push('Nominal harus lebih dari 0');
    }
    if (memberData.payment_amount && memberData.payment_amount > 50000000) {
      errors.push('Nominal maksimal Rp 50.000.000');
    }

    // Validasi tanggal
    if (memberData.payment_date) {
      const paymentDate = new Date(memberData.payment_date);
      const today = new Date();
      const oneYearAgo = new Date();
      oneYearAgo.setFullYear(today.getFullYear() - 1);

      if (paymentDate > today) {
        errors.push('Tanggal pembayaran tidak boleh di masa depan');
      }
      if (paymentDate < oneYearAgo) {
        errors.push('Tanggal pembayaran tidak boleh lebih dari 1 tahun yang lalu');
      }
    }

    // Validasi status
    if (!['paid', 'unpaid'].includes(memberData.payment_status)) {
      errors.push('Status pembayaran tidak valid');
    }

    return errors;
  };

  const addMember = async (memberData: Omit<MemberData, 'id'>) => {
    try {
      // Validasi data
      const validationErrors = validateMemberData(memberData);
      if (validationErrors.length > 0) {
        throw new Error(`❌ VALIDASI GAGAL: ${validationErrors.join(', ')}`);
      }

      await addDoc(membersCollection, {
        ...memberData,
        name: memberData.name.trim(), // Trim whitespace
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      });
    } catch (error) {
      console.error('❌ Error adding member:', error);
      throw error;
    }
  };

  const updateMember = async (id: string, updates: Partial<MemberData>) => {
    try {
      const memberDoc = doc(db, 'members', id);
      await updateDoc(memberDoc, {
        ...updates,
        updatedAt: serverTimestamp()
      });
    } catch (error) {
      console.error('Error updating member:', error);
      throw error;
    }
  };

  const deleteMember = async (id: string) => {
    try {
      const memberDoc = doc(db, 'members', id);
      await deleteDoc(memberDoc);
    } catch (error) {
      console.error('Error deleting member:', error);
      throw error;
    }
  };

  const toggleMemberStatus = async (id: string) => {
    try {
      const member = members.find(m => m.id === id);
      if (member) {
        const newStatus = member.payment_status === 'paid' ? 'unpaid' : 'paid';
        await updateMember(id, { payment_status: newStatus });
      }
    } catch (error) {
      console.error('Error toggling member status:', error);
      throw error;
    }
  };

  // Optimized functions with useCallback
  const setPage = useCallback((page: number) => {
    setCurrentPage(page);
  }, []);

  const setItemsPerPage = useCallback((items: number) => {
    setItemsPerPageState(items);
    setCurrentPage(1); // Reset to first page
  }, []);

  const searchMembers = useCallback((query: string) => {
    setSearchQuery(query);
    setCurrentPage(1); // Reset to first page
  }, []);

  const filterMembers = useCallback((status: 'all' | 'paid' | 'unpaid') => {
    setFilterStatus(status);
    setCurrentPage(1); // Reset to first page
  }, []);

  // Optimized context value with useMemo
  const value: MembersContextType = useMemo(() => ({
    members,
    allMembers,
    loading,
    currentPage,
    totalPages: filteredAndPaginatedMembers.totalPages,
    itemsPerPage,
    totalMembers: filteredAndPaginatedMembers.totalMembers,
    addMember,
    updateMember,
    deleteMember,
    toggleMemberStatus,
    setPage,
    setItemsPerPage,
    searchMembers,
    filterMembers,
  }), [
    members,
    allMembers,
    loading,
    currentPage,
    filteredAndPaginatedMembers.totalPages,
    filteredAndPaginatedMembers.totalMembers,
    itemsPerPage,
    addMember,
    updateMember,
    deleteMember,
    toggleMemberStatus,
    setPage,
    setItemsPerPage,
    searchMembers,
    filterMembers,
  ]);

  return (
    <MembersContext.Provider value={value}>
      {children}
    </MembersContext.Provider>
  );
};

export default MembersContext;
