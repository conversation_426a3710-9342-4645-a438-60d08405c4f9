#!/usr/bin/env node

/**
 * Bundle analyzer script untuk menganalisis ukuran bundle
 * Jalankan dengan: node scripts/bundle-analyzer.js
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

function formatBytes(bytes, decimals = 2) {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
}

function analyzeBundleSize() {
  const distPath = path.join(__dirname, '../dist');
  
  if (!fs.existsSync(distPath)) {
    console.error('❌ Build not found. Run npm run build first.');
    process.exit(1);
  }

  console.log('📊 Bundle Size Analysis\n');

  // Get all files in dist directory
  const files = [];
  
  function scanDirectory(dir, prefix = '') {
    const items = fs.readdirSync(dir);
    
    items.forEach(item => {
      const itemPath = path.join(dir, item);
      const stat = fs.statSync(itemPath);
      
      if (stat.isDirectory()) {
        scanDirectory(itemPath, prefix + item + '/');
      } else {
        const relativePath = prefix + item;
        files.push({
          name: relativePath,
          path: itemPath,
          size: stat.size,
          type: path.extname(item)
        });
      }
    });
  }

  scanDirectory(distPath);

  // Categorize files
  const categories = {
    javascript: files.filter(f => f.type === '.js'),
    css: files.filter(f => f.type === '.css'),
    html: files.filter(f => f.type === '.html'),
    assets: files.filter(f => !['.js', '.css', '.html'].includes(f.type))
  };

  // Calculate totals
  let totalSize = 0;
  const categoryTotals = {};

  Object.entries(categories).forEach(([category, categoryFiles]) => {
    const categorySize = categoryFiles.reduce((sum, file) => sum + file.size, 0);
    categoryTotals[category] = categorySize;
    totalSize += categorySize;
  });

  // Display results
  console.log('📁 File Categories:');
  Object.entries(categoryTotals).forEach(([category, size]) => {
    const percentage = ((size / totalSize) * 100).toFixed(1);
    console.log(`   ${category.toUpperCase()}: ${formatBytes(size)} (${percentage}%)`);
  });

  console.log(`\n📊 Total Bundle Size: ${formatBytes(totalSize)}\n`);

  // Detailed JavaScript analysis
  if (categories.javascript.length > 0) {
    console.log('🔍 JavaScript Files Analysis:');
    
    const sortedJS = categories.javascript.sort((a, b) => b.size - a.size);
    
    sortedJS.forEach(file => {
      const percentage = ((file.size / totalSize) * 100).toFixed(1);
      const status = file.size > 500 * 1024 ? '🔴' : file.size > 100 * 1024 ? '🟡' : '🟢';
      console.log(`   ${status} ${file.name}: ${formatBytes(file.size)} (${percentage}%)`);
    });
  }

  // Performance recommendations
  console.log('\n💡 Performance Recommendations:');
  
  const largeFiles = files.filter(f => f.size > 500 * 1024);
  if (largeFiles.length > 0) {
    console.log('   🔴 Large files detected (>500KB):');
    largeFiles.forEach(file => {
      console.log(`      - ${file.name}: ${formatBytes(file.size)}`);
    });
    console.log('   💡 Consider code splitting or lazy loading for these files');
  }

  const mediumFiles = files.filter(f => f.size > 100 * 1024 && f.size <= 500 * 1024);
  if (mediumFiles.length > 0) {
    console.log('   🟡 Medium files (100-500KB):');
    mediumFiles.forEach(file => {
      console.log(`      - ${file.name}: ${formatBytes(file.size)}`);
    });
    console.log('   💡 Monitor these files for potential optimization');
  }

  // Bundle size targets
  console.log('\n🎯 Bundle Size Targets:');
  console.log('   🟢 Excellent: <300KB');
  console.log('   🟡 Good: 300-500KB');
  console.log('   🟠 Acceptable: 500KB-1MB');
  console.log('   🔴 Needs Optimization: >1MB');

  const totalKB = Math.round(totalSize / 1024);
  let status, recommendation;
  
  if (totalKB < 300) {
    status = '🟢 EXCELLENT';
    recommendation = 'Bundle size is optimal!';
  } else if (totalKB < 500) {
    status = '🟡 GOOD';
    recommendation = 'Bundle size is acceptable but can be improved.';
  } else if (totalKB < 1000) {
    status = '🟠 ACCEPTABLE';
    recommendation = 'Bundle size should be optimized for better performance.';
  } else {
    status = '🔴 NEEDS OPTIMIZATION';
    recommendation = 'Bundle size is too large and needs immediate optimization.';
  }

  console.log(`\n📈 Current Status: ${status}`);
  console.log(`💬 ${recommendation}`);

  // Optimization suggestions
  if (totalKB > 500) {
    console.log('\n🛠️ Optimization Suggestions:');
    console.log('   1. Implement route-based code splitting');
    console.log('   2. Lazy load heavy components');
    console.log('   3. Remove unused dependencies');
    console.log('   4. Optimize images and assets');
    console.log('   5. Use tree shaking for libraries');
    console.log('   6. Consider using lighter alternatives for heavy libraries');
  }

  // Gzip estimation
  console.log('\n📦 Estimated Gzip Sizes:');
  Object.entries(categoryTotals).forEach(([category, size]) => {
    const gzipSize = Math.round(size * 0.3); // Rough gzip estimation
    console.log(`   ${category.toUpperCase()}: ~${formatBytes(gzipSize)} (gzipped)`);
  });

  const totalGzipSize = Math.round(totalSize * 0.3);
  console.log(`   TOTAL: ~${formatBytes(totalGzipSize)} (gzipped)`);

  // Exit with appropriate code
  if (totalKB > 1000) {
    console.log('\n❌ Bundle size exceeds 1MB - optimization required!');
    process.exit(1);
  } else if (totalKB > 500) {
    console.log('\n⚠️ Bundle size could be optimized further.');
    process.exit(0);
  } else {
    console.log('\n✅ Bundle size is within acceptable limits.');
    process.exit(0);
  }
}

analyzeBundleSize();
