#!/usr/bin/env node

/**
 * Script untuk memvalidasi environment variables
 * Jalankan dengan: node scripts/validate-env.js
 */

import fs from 'fs';
import path from 'path';

const requiredEnvVars = [
  'VITE_FIREBASE_API_KEY',
  'VITE_FIREBASE_AUTH_DOMAIN', 
  'VITE_FIREBASE_PROJECT_ID',
  'VITE_FIREBASE_STORAGE_BUCKET',
  'VITE_FIREBASE_MESSAGING_SENDER_ID',
  'VITE_FIREBASE_APP_ID'
];

const optionalEnvVars = [
  'VITE_FIREBASE_MEASUREMENT_ID',
  'VITE_WHATSAPP_API_URL',
  'VITE_WHATSAPP_API_KEY'
];

function validateEnvironment() {
  console.log('🔍 Validating environment configuration...\n');

  // Check if .env file exists
  const envPath = path.join(process.cwd(), '.env');
  if (!fs.existsSync(envPath)) {
    console.error('❌ .env file not found!');
    console.log('📝 Please copy .env.example to .env and fill in your credentials:');
    console.log('   cp .env.example .env\n');
    process.exit(1);
  }

  // Read .env file
  const envContent = fs.readFileSync(envPath, 'utf8');
  const envLines = envContent.split('\n').filter(line => line.trim() && !line.startsWith('#'));
  const envVars = {};
  
  envLines.forEach(line => {
    const [key, ...valueParts] = line.split('=');
    if (key && valueParts.length > 0) {
      envVars[key.trim()] = valueParts.join('=').trim();
    }
  });

  let hasErrors = false;
  let hasWarnings = false;

  // Check required variables
  console.log('🔑 Required Firebase credentials:');
  requiredEnvVars.forEach(envVar => {
    const value = envVars[envVar];
    if (!value || value === 'your_firebase_api_key_here' || value.includes('your_')) {
      console.log(`   ❌ ${envVar}: Missing or placeholder value`);
      hasErrors = true;
    } else {
      console.log(`   ✅ ${envVar}: Set`);
    }
  });

  // Check optional variables
  console.log('\n🔧 Optional configuration:');
  optionalEnvVars.forEach(envVar => {
    const value = envVars[envVar];
    if (!value || value.includes('your_')) {
      console.log(`   ⚠️  ${envVar}: Not set (optional)`);
      hasWarnings = true;
    } else {
      console.log(`   ✅ ${envVar}: Set`);
    }
  });

  // Security check
  console.log('\n🔒 Security validation:');
  
  // Check for placeholder values
  const placeholderPatterns = [
    'your_firebase_api_key_here',
    'your_project_id',
    'your_api_key',
    'your_whatsapp_api_url'
  ];
  
  let hasPlaceholders = false;
  Object.entries(envVars).forEach(([key, value]) => {
    placeholderPatterns.forEach(pattern => {
      if (value.includes(pattern)) {
        console.log(`   ❌ ${key}: Contains placeholder value`);
        hasPlaceholders = true;
      }
    });
  });

  if (!hasPlaceholders) {
    console.log('   ✅ No placeholder values found');
  }

  // Check .gitignore
  const gitignorePath = path.join(process.cwd(), '.gitignore');
  if (fs.existsSync(gitignorePath)) {
    const gitignoreContent = fs.readFileSync(gitignorePath, 'utf8');
    if (gitignoreContent.includes('.env')) {
      console.log('   ✅ .env file is properly gitignored');
    } else {
      console.log('   ❌ .env file is NOT gitignored - SECURITY RISK!');
      hasErrors = true;
    }
  }

  // Summary
  console.log('\n📊 Validation Summary:');
  if (hasErrors) {
    console.log('❌ Environment setup has ERRORS - please fix before proceeding');
    console.log('\n💡 Next steps:');
    console.log('1. Copy .env.example to .env if not done');
    console.log('2. Get Firebase credentials from Firebase Console');
    console.log('3. Replace all placeholder values with actual credentials');
    console.log('4. Ensure .env is in .gitignore');
    process.exit(1);
  } else if (hasWarnings) {
    console.log('⚠️  Environment setup is functional but has warnings');
    console.log('✅ All required credentials are set - you can proceed');
  } else {
    console.log('✅ Environment setup is perfect!');
  }

  console.log('\n🚀 Ready to run: npm run dev');
}

validateEnvironment();
