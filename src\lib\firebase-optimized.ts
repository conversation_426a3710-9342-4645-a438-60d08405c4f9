// Optimized Firebase configuration with minimal imports for tree shaking
import { initializeApp } from "firebase/app";
import { getAuth } from 'firebase/auth';
import { 
  getFirestore,
  connectFirestoreEmulator,
  enableNetwork,
  disableNetwork,
  // Only import what we actually use
  collection,
  doc,
  addDoc,
  updateDoc,
  deleteDoc,
  getDocs,
  onSnapshot,
  query,
  orderBy,
  limit,
  serverTimestamp
} from 'firebase/firestore';

// Validate required environment variables
const requiredEnvVars = [
  'VITE_FIREBASE_API_KEY',
  'VITE_FIREBASE_AUTH_DOMAIN',
  'VITE_FIREBASE_PROJECT_ID',
  'VITE_FIREBASE_STORAGE_BUCKET',
  'VITE_FIREBASE_MESSAGING_SENDER_ID',
  'VITE_FIREBASE_APP_ID'
];

const missingEnvVars = requiredEnvVars.filter(envVar => !import.meta.env[envVar]);

if (missingEnvVars.length > 0) {
  throw new Error(
    `Missing required Firebase environment variables: ${missingEnvVars.join(', ')}\n` +
    'Please check your .env file and ensure all Firebase credentials are set.'
  );
}

// Minimal Firebase configuration
const firebaseConfig = {
  apiKey: import.meta.env.VITE_FIREBASE_API_KEY,
  authDomain: import.meta.env.VITE_FIREBASE_AUTH_DOMAIN,
  projectId: import.meta.env.VITE_FIREBASE_PROJECT_ID,
  storageBucket: import.meta.env.VITE_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: import.meta.env.VITE_FIREBASE_MESSAGING_SENDER_ID,
  appId: import.meta.env.VITE_FIREBASE_APP_ID,
  measurementId: import.meta.env.VITE_FIREBASE_MEASUREMENT_ID
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize only the services we need
export const auth = getAuth(app);
export const db = getFirestore(app);

// Export commonly used Firestore functions to reduce imports
export {
  collection,
  doc,
  addDoc,
  updateDoc,
  deleteDoc,
  getDocs,
  onSnapshot,
  query,
  orderBy,
  limit,
  serverTimestamp,
  enableNetwork,
  disableNetwork,
  connectFirestoreEmulator
};

// Chrome compatibility fix with minimal impact
if (typeof window !== 'undefined') {
  // Only load cache clearing if needed
  const clearCache = async () => {
    try {
      const { clearIndexedDbPersistence } = await import('firebase/firestore');
      await clearIndexedDbPersistence(db);
    } catch (error) {
      // Ignore errors, cache might not exist
    }
  };
  
  // Clear cache on first load only
  if (!sessionStorage.getItem('firebase-cache-cleared')) {
    clearCache();
    sessionStorage.setItem('firebase-cache-cleared', 'true');
  }
}

export default app;
