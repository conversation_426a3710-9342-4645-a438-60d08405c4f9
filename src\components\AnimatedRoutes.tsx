import { Suspense, lazy } from 'react';
import { AnimatePresence } from 'framer-motion';
import { Routes, Route, useLocation, Navigate, Outlet } from 'react-router-dom';
import Layout from './Layout';
import AdminLayout from './AdminLayout';
import PageTransition from './PageTransition';
import { useAuth } from '../context/AuthContext';
import Loader from './Loader';
import { PageErrorBoundary } from './ErrorBoundary';
import { ExpensesProvider } from '../context/ExpensesContext';
import { MembersProvider } from '../context/MembersContext';
import { EventsProvider } from '../context/EventsContext';
import { DuesConfigProvider } from '../context/DuesConfigContext';

// Lazy load pages for better performance
const FinancePage = lazy(() => import('../pages/FinancePage'));
const MembersPage = lazy(() => import('../pages/MembersPage'));
const EventsPage = lazy(() => import('../pages/EventsPage'));
const AdminLoginPage = lazy(() => import('../pages/AdminLoginPage'));
const AdminPage = lazy(() => import('../pages/AdminPage'));
const AdminMembersPage = lazy(() => import('../pages/admin/AdminMembersPage'));
const AdminEventsPage = lazy(() => import('../pages/admin/AdminEventsPage'));
const AdminExpensesPage = lazy(() => import('../pages/admin/AdminExpensesPage'));
const AdminDuesSettingsPage = lazy(() => import('../pages/admin/AdminDuesSettingsPage'));
const NotFound = lazy(() => import('../pages/NotFound'));

const ProtectedRoute = () => {
  const { isAuthenticated, isLoading } = useAuth();
  const location = useLocation();

  if (isLoading) {
    return (
      <div className="min-h-screen w-full flex items-center justify-center bg-[#F9F9F9]">
        <Loader text="Memeriksa sesi admin..." />
      </div>
    );
  }

  return isAuthenticated
    ? <Outlet />
    : <Navigate to="/admin/login" state={{ from: location }} replace />;
};

const AnimatedRoutes = () => {
  const location = useLocation();
  
  return (
    <AnimatePresence mode="wait">
      <Routes location={location} key={location.pathname}>
        <Route
          path="/"
          element={
            <PageErrorBoundary>
              <Layout>
                <PageTransition>
                  <ExpensesProvider>
                    <MembersProvider>
                      <Suspense fallback={<Loader text="Memuat halaman keuangan..." />}>
                        <FinancePage />
                      </Suspense>
                    </MembersProvider>
                  </ExpensesProvider>
                </PageTransition>
              </Layout>
            </PageErrorBoundary>
          }
        />
        <Route
          path="/members"
          element={
            <PageErrorBoundary>
              <Layout>
                <PageTransition>
                  <MembersProvider>
                    <Suspense fallback={<Loader text="Memuat data anggota..." />}>
                      <MembersPage />
                    </Suspense>
                  </MembersProvider>
                </PageTransition>
              </Layout>
            </PageErrorBoundary>
          }
        />
        <Route
          path="/events"
          element={
            <PageErrorBoundary>
              <Layout>
                <PageTransition>
                  <EventsProvider>
                    <DuesConfigProvider>
                      <Suspense fallback={<Loader text="Memuat data acara..." />}>
                        <EventsPage />
                      </Suspense>
                    </DuesConfigProvider>
                  </EventsProvider>
                </PageTransition>
              </Layout>
            </PageErrorBoundary>
          }
        />
        
        <Route
          path="/admin/login"
          element={
            <PageErrorBoundary>
              <PageTransition>
                <Suspense fallback={<Loader text="Memuat halaman login..." />}>
                  <AdminLoginPage />
                </Suspense>
              </PageTransition>
            </PageErrorBoundary>
          }
        />
        
        <Route element={<ProtectedRoute />}>
          <Route
            path="/admin"
            element={
              <AdminLayout>
                <PageTransition>
                  <AdminPage />
                </PageTransition>
              </AdminLayout>
            }
          />
          <Route
            path="/admin/members"
            element={
              <AdminLayout>
                <PageTransition>
                  <AdminMembersPage />
                </PageTransition>
              </AdminLayout>
            }
          />
          <Route
            path="/admin/events"
            element={
              <AdminLayout>
                <PageTransition>
                  <AdminEventsPage />
                </PageTransition>
              </AdminLayout>
            }
          />
          <Route
            path="/admin/expenses"
            element={
              <AdminLayout>
                <PageTransition>
                  <AdminExpensesPage />
                </PageTransition>
              </AdminLayout>
            }
          />
          <Route
            path="/admin/dues-settings"
            element={
              <AdminLayout>
                <PageTransition>
                  <AdminDuesSettingsPage />
                </PageTransition>
              </AdminLayout>
            }
          />

        </Route>
        
        <Route 
          path="*" 
          element={
            <PageTransition>
              <NotFound />
            </PageTransition>
          } 
        />
      </Routes>
    </AnimatePresence>
  );
};

export default AnimatedRoutes; 