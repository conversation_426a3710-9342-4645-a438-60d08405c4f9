import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON>alog<PERSON>eader, DialogTitle } from './ui/dialog';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Label } from './ui/label';
import { MemberData } from '../hooks/useMembers';

type MemberFormData = Omit<MemberData, 'id'>;

interface MemberFormModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (member: MemberFormData) => void;
  member?: MemberData;
}

export default function MemberFormModal({ isOpen, onClose, onSubmit, member }: MemberFormModalProps) {
  const [formData, setFormData] = useState<MemberFormData>({
    name: '',
    payment_amount: 0,
    payment_status: 'unpaid',
    payment_date: null,
  });

  useEffect(() => {
    if (member) {
      setFormData({
        name: member.name,
        payment_status: member.payment_status,
        payment_amount: member.payment_amount,
        payment_date: member.payment_date,
      });
    }
  }, [member]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.name || formData.payment_amount <= 0) {
      alert('Mohon isi semua field yang diperlukan');
      return;
    }
    // Set payment date automatically for new members
    const dataToSubmit = {
      ...formData,
      payment_date: new Date().toISOString()
    };
    onSubmit(dataToSubmit);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>{member ? 'Edit Anggota' : 'Tambah Anggota'}</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="name">Nama</Label>
            <Input
              id="name"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              required
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="payment_status">Status</Label>
            <select
              id="payment_status"
              value={formData.payment_status}
              onChange={(e) => setFormData({ ...formData, payment_status: e.target.value as 'paid' | 'unpaid' })}
              className="w-full rounded-md border border-gray-300 p-2"
              required
              aria-label="Status Pembayaran"
            >
              <option value="unpaid">Belum Lunas</option>
              <option value="paid">Lunas</option>
            </select>
          </div>
          <div className="space-y-2">
            <Label htmlFor="payment_amount">Jumlah</Label>
            <Input
              id="payment_amount"
              type="number"
              value={formData.payment_amount}
              onChange={(e) => setFormData({ ...formData, payment_amount: Number(e.target.value) })}
              required
            />
          </div>
          <div className="flex justify-end space-x-2">
            <Button type="button" variant="outline" onClick={onClose}>
              Batal
            </Button>
            <Button type="submit">
              {member ? 'Simpan' : 'Tambah'}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
} 