import React, { useState, useEffect } from 'react';
import { RefreshCw, X, Download } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { useRegisterSW } from 'virtual:pwa-register/react';

const PWAUpdateNotification: React.FC = () => {
  const [showUpdatePrompt, setShowUpdatePrompt] = useState(false);
  
  const {
    offlineReady: [offlineReady, setOfflineReady],
    needRefresh: [needRefresh, setNeedRefresh],
    updateServiceWorker,
  } = useRegisterSW({
    onRegistered(r) {
    },
    onRegisterError(error) {
      console.error('❌ SW registration error', error);
    },
  });

  useEffect(() => {
    if (needRefresh) {
      setShowUpdatePrompt(true);
    }
  }, [needRefresh]);

  const handleUpdate = () => {
    updateServiceWorker(true);
    setShowUpdatePrompt(false);
  };

  const handleDismiss = () => {
    setShowUpdatePrompt(false);
    setNeedRefresh(false);
  };

  const handleOfflineReady = () => {
    setOfflineReady(false);
  };

  return (
    <>
      {/* Update Available Notification */}
      <AnimatePresence>
        {showUpdatePrompt && (
          <motion.div
            initial={{ opacity: 0, y: -100 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -100 }}
            className="fixed top-4 left-4 right-4 md:left-auto md:right-4 md:max-w-sm z-50"
          >
            <div className="bg-blue-50 border border-blue-200 rounded-lg shadow-lg p-4">
              <div className="flex items-start justify-between mb-3">
                <div className="flex items-center space-x-2">
                  <div className="p-2 bg-blue-500 rounded-lg">
                    <RefreshCw size={20} className="text-white" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-blue-900 text-sm">Update Available</h3>
                    <p className="text-xs text-blue-700">A new version of OSIS is ready</p>
                  </div>
                </div>
                <button
                  onClick={handleDismiss}
                  className="text-blue-400 hover:text-blue-600 transition-colors"
                >
                  <X size={16} />
                </button>
              </div>

              <div className="flex space-x-2">
                <button
                  onClick={handleUpdate}
                  className="flex-1 bg-blue-500 text-white px-3 py-2 rounded-lg text-sm font-medium hover:bg-blue-600 transition-colors flex items-center justify-center space-x-1"
                >
                  <Download size={16} />
                  <span>Update Now</span>
                </button>
                <button
                  onClick={handleDismiss}
                  className="px-3 py-2 text-blue-600 text-sm font-medium hover:text-blue-800 transition-colors"
                >
                  Later
                </button>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Offline Ready Notification */}
      <AnimatePresence>
        {offlineReady && (
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.9 }}
            className="fixed bottom-4 left-4 right-4 md:left-auto md:right-4 md:max-w-sm z-50"
          >
            <div className="bg-green-50 border border-green-200 rounded-lg shadow-lg p-4">
              <div className="flex items-start justify-between">
                <div className="flex items-center space-x-2">
                  <div className="p-2 bg-green-500 rounded-lg">
                    <Download size={20} className="text-white" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-green-900 text-sm">Ready for Offline</h3>
                    <p className="text-xs text-green-700">OSIS can now work without internet</p>
                  </div>
                </div>
                <button
                  onClick={handleOfflineReady}
                  className="text-green-400 hover:text-green-600 transition-colors"
                >
                  <X size={16} />
                </button>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
};

export default PWAUpdateNotification;
