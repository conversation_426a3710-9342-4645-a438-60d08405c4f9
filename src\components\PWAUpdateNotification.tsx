import React, { useState, useEffect } from 'react';
import { RefreshCw, X } from 'lucide-react';

const PWAUpdateNotification: React.FC = () => {
  const [showUpdatePrompt, setShowUpdatePrompt] = useState(false);

  useEffect(() => {
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.register('/sw.js')
        .then((registration) => {
          registration.addEventListener('updatefound', () => {
            const newWorker = registration.installing;
            if (newWorker) {
              newWorker.addEventListener('statechange', () => {
                if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                  setShowUpdatePrompt(true);
                }
              });
            }
          });
        })
        .catch(() => {
          // Silent fail
        });
    }
  }, []);

  const handleUpdate = () => {
    window.location.reload();
  };

  const handleDismiss = () => {
    setShowUpdatePrompt(false);
  };

  return (
    <>
      {showUpdatePrompt && (
        <div className="fixed top-4 right-4 max-w-sm z-50">
          <div className="bg-blue-50 border border-blue-200 rounded-lg shadow-lg p-4">
            <div className="flex items-start justify-between mb-3">
              <div className="flex items-center space-x-2">
                <RefreshCw size={16} className="text-blue-500" />
                <div>
                  <h3 className="font-semibold text-blue-900 text-sm">Update Available</h3>
                  <p className="text-xs text-blue-700">New version ready</p>
                </div>
              </div>
              <button
                type="button"
                onClick={handleDismiss}
                className="text-blue-400 hover:text-blue-600"
                title="Dismiss"
              >
                <X size={14} />
              </button>
            </div>
            <div className="flex space-x-2">
              <button
                type="button"
                onClick={handleUpdate}
                className="flex-1 bg-blue-500 text-white px-3 py-1 rounded text-sm hover:bg-blue-600"
              >
                Update
              </button>
              <button
                type="button"
                onClick={handleDismiss}
                className="px-3 py-1 text-blue-600 text-sm hover:text-blue-800"
              >
                Later
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default PWAUpdateNotification;
