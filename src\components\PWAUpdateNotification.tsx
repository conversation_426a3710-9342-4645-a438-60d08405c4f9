import React, { useState, useEffect } from 'react';
import { RefreshCw, X, Download } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';

const PWAUpdateNotification: React.FC = () => {
  const [showUpdatePrompt, setShowUpdatePrompt] = useState(false);
  const [needRefresh, setNeedRefresh] = useState(false);

  // Simple service worker registration without virtual module
  useEffect(() => {
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.register('/sw.js')
        .then((registration) => {
          registration.addEventListener('updatefound', () => {
            const newWorker = registration.installing;
            if (newWorker) {
              newWorker.addEventListener('statechange', () => {
                if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                  setNeedRefresh(true);
                }
              });
            }
          });
        })
        .catch((error) => {
          console.error('❌ SW registration error', error);
        });
    }
  }, []);

  useEffect(() => {
    if (needRefresh) {
      setShowUpdatePrompt(true);
    }
  }, [needRefresh]);

  const handleUpdate = () => {
    // Reload page to activate new service worker
    window.location.reload();
  };

  const handleDismiss = () => {
    setShowUpdatePrompt(false);
    setNeedRefresh(false);
  };

  return (
    <>
      {/* Update Available Notification */}
      <AnimatePresence>
        {showUpdatePrompt && (
          <motion.div
            initial={{ opacity: 0, y: -100 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -100 }}
            className="fixed top-4 left-4 right-4 md:left-auto md:right-4 md:max-w-sm z-50"
          >
            <div className="bg-blue-50 border border-blue-200 rounded-lg shadow-lg p-4">
              <div className="flex items-start justify-between mb-3">
                <div className="flex items-center space-x-2">
                  <div className="p-2 bg-blue-500 rounded-lg">
                    <RefreshCw size={20} className="text-white" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-blue-900 text-sm">Update Available</h3>
                    <p className="text-xs text-blue-700">A new version of OSIS is ready</p>
                  </div>
                </div>
                <button
                  type="button"
                  onClick={handleDismiss}
                  className="text-blue-400 hover:text-blue-600 transition-colors"
                  title="Dismiss update notification"
                >
                  <X size={16} />
                </button>
              </div>

              <div className="flex space-x-2">
                <button
                  type="button"
                  onClick={handleUpdate}
                  className="flex-1 bg-blue-500 text-white px-3 py-2 rounded-lg text-sm font-medium hover:bg-blue-600 transition-colors flex items-center justify-center space-x-1"
                >
                  <Download size={16} />
                  <span>Update Now</span>
                </button>
                <button
                  type="button"
                  onClick={handleDismiss}
                  className="px-3 py-2 text-blue-600 text-sm font-medium hover:text-blue-800 transition-colors"
                >
                  Later
                </button>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Offline Ready Notification - Removed as requested */}
    </>
  );
};

export default PWAUpdateNotification;
