import { Calendar, Clock, MapPin } from 'lucide-react';
import { Event } from '../services/api';
import { formatDate } from '../utils/formatters';

interface EventCardProps {
  event: Event;
  onClick: () => void;
}

const EventCard: React.FC<EventCardProps> = ({ event, onClick }) => {
  return (
    <div 
      className="neo-card bg-gradient-to-br from-white to-[#FCE09B]/50 p-3 sm:p-4 cursor-pointer animate-fade-in border-4 border-[#5D534B] rounded-2xl transition-all duration-200 hover:shadow-xl pointer-events-auto touch-auto" 
      onClick={onClick}
    >
      <h3 className="text-lg font-bold mb-2 text-[#5D534B] break-words">{event.title}</h3>
      
      <div className="flex items-center mb-2 text-xs sm:text-sm text-[#5D534B]">
        <Calendar size={14} className="mr-2 text-[#9DE0D2] flex-shrink-0" />
        <span className="truncate">{formatDate(event.date)}</span>
      </div>
      
      <div className="flex items-center mb-2 text-xs sm:text-sm text-[#5D534B]">
        <Clock size={14} className="mr-2 text-[#FF9898] flex-shrink-0" />
        <span className="truncate">{event.time}</span>
      </div>
      
      <div className="flex items-center text-xs sm:text-sm text-[#5D534B]">
        <MapPin size={14} className="mr-2 text-[#9DE0D2] flex-shrink-0" />
        <span className="truncate">{event.location}</span>
      </div>
      
      <button 
        className="bg-[#FCE09B] text-[#5D534B] border-2 sm:border-4 border-[#5D534B] rounded-full px-3 sm:px-4 py-1 mt-3 w-full text-xs sm:text-sm font-bold shadow-pastel-sm transition-all duration-200 active:scale-95 active:bg-[#f9d572]" 
        onClick={(e) => {
          e.stopPropagation();
          onClick();
        }}
      >
        Lihat Detail
      </button>
    </div>
  );
};

export default EventCard;
