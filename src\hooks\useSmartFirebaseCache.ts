import { useState, useEffect, useCallback, useRef } from 'react';
import { 
  Query, 
  onSnapshot, 
  DocumentData, 
  QuerySnapshot,
  FirestoreError,
  getDocs 
} from 'firebase/firestore';
import { advancedCache, cacheUtils } from '../utils/advancedCache';

interface SmartCacheOptions {
  enableRealtime?: boolean;
  cacheFirst?: boolean;
  backgroundRefresh?: boolean;
  staleWhileRevalidate?: boolean;
  maxAge?: number; // Maximum age in milliseconds
  retryAttempts?: number;
  onError?: (error: FirestoreError) => void;
}

interface CacheMetadata {
  lastFetch: number;
  version: string;
  source: 'cache' | 'network' | 'realtime';
}

export function useSmartFirebaseCache<T extends DocumentData>(
  query: Query<T>,
  collectionName: string,
  options: SmartCacheOptions = {}
) {
  const {
    enableRealtime = true,
    cacheFirst = true,
    backgroundRefresh = true,
    staleWhileRevalidate = true,
    maxAge = 30 * 60 * 1000, // 30 minutes default
    retryAttempts = 3,
    onError
  } = options;

  const [data, setData] = useState<T[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<FirestoreError | null>(null);
  const [metadata, setMetadata] = useState<CacheMetadata | null>(null);
  
  const unsubscribeRef = useRef<(() => void) | null>(null);
  const retryCountRef = useRef(0);
  const queryKeyRef = useRef<string>('');

  // Generate cache key from query
  const generateCacheKey = useCallback((query: Query): string => {
    // Create a deterministic key from query parameters
    const queryStr = query.toString();
    return `${collectionName}:${btoa(queryStr).slice(0, 32)}`;
  }, [collectionName]);

  // Load data from cache
  const loadFromCache = useCallback(async (cacheKey: string): Promise<T[] | null> => {
    try {
      const cachedData = await cacheUtils.getCachedFirebaseData<{
        data: T[];
        metadata: CacheMetadata;
      }>(cacheKey, collectionName);

      if (cachedData) {
        const age = Date.now() - cachedData.metadata.lastFetch;
        
        // Check if cache is still valid
        if (age <= maxAge) {
          setData(cachedData.data);
          setMetadata(cachedData.metadata);
          setLoading(false);
          return cachedData.data;
        }
        
        // If stale-while-revalidate, use stale data but trigger refresh
        if (staleWhileRevalidate && age <= maxAge * 2) {
          setData(cachedData.data);
          setMetadata({ ...cachedData.metadata, source: 'cache' });
          setLoading(false);
          return cachedData.data;
        }
      }
    } catch (error) {
    }
    
    return null;
  }, [collectionName, maxAge, staleWhileRevalidate]);

  // Save data to cache
  const saveToCache = useCallback(async (
    cacheKey: string, 
    data: T[], 
    source: 'network' | 'realtime'
  ): Promise<void> => {
    try {
      const metadata: CacheMetadata = {
        lastFetch: Date.now(),
        version: '1.0',
        source
      };

      await cacheUtils.cacheFirebaseData(cacheKey, {
        data,
        metadata
      }, collectionName);

      setMetadata(metadata);
    } catch (error) {
    }
  }, [collectionName]);

  // Fetch data from network
  const fetchFromNetwork = useCallback(async (
    query: Query<T>, 
    cacheKey: string,
    isRetry = false
  ): Promise<T[]> => {
    try {
      setError(null);
      
      if (!isRetry) {
        setLoading(true);
      }

      const snapshot = await getDocs(query);
      const newData: T[] = [];
      
      snapshot.forEach((doc) => {
        newData.push({
          id: doc.id,
          ...doc.data()
        } as T);
      });

      setData(newData);
      setLoading(false);
      retryCountRef.current = 0;

      // Save to cache
      await saveToCache(cacheKey, newData, 'network');

      return newData;
    } catch (err) {
      const firestoreError = err as FirestoreError;
      
      // Retry logic
      if (retryCountRef.current < retryAttempts) {
        retryCountRef.current++;
        
        // Exponential backoff
        const delay = Math.pow(2, retryCountRef.current) * 1000;
        await new Promise(resolve => setTimeout(resolve, delay));
        
        return fetchFromNetwork(query, cacheKey, true);
      }

      setError(firestoreError);
      setLoading(false);
      onError?.(firestoreError);
      
      throw firestoreError;
    }
  }, [retryAttempts, onError, saveToCache]);

  // Setup realtime listener
  const setupRealtimeListener = useCallback((query: Query<T>, cacheKey: string) => {
    if (!enableRealtime) return;

    const unsubscribe = onSnapshot(
      query,
      (snapshot: QuerySnapshot<T>) => {
        const newData: T[] = [];
        
        snapshot.forEach((doc) => {
          newData.push({
            id: doc.id,
            ...doc.data()
          } as T);
        });

        setData(newData);
        setLoading(false);
        setError(null);

        // Save realtime data to cache
        saveToCache(cacheKey, newData, 'realtime');
      },
      (error: FirestoreError) => {
        setError(error);
        setLoading(false);
        onError?.(error);
      }
    );

    unsubscribeRef.current = unsubscribe;
  }, [enableRealtime, onError, saveToCache]);

  // Main data loading logic
  const loadData = useCallback(async () => {
    const cacheKey = generateCacheKey(query);
    queryKeyRef.current = cacheKey;

    try {
      // 1. Try cache first if enabled
      if (cacheFirst) {
        const cachedData = await loadFromCache(cacheKey);
        
        if (cachedData && !staleWhileRevalidate) {
          // Fresh cache hit, setup realtime if needed
          if (enableRealtime) {
            setupRealtimeListener(query, cacheKey);
          }
          return;
        }
      }

      // 2. Fetch from network
      if (!enableRealtime) {
        // One-time fetch
        await fetchFromNetwork(query, cacheKey);
      } else {
        // Setup realtime listener (will also fetch initial data)
        setupRealtimeListener(query, cacheKey);
        
        // If no cache or stale cache, also do immediate fetch
        if (!cacheFirst || staleWhileRevalidate) {
          try {
            await fetchFromNetwork(query, cacheKey);
          } catch {
            // Ignore errors if realtime listener is active
          }
        }
      }

      // 3. Background refresh if enabled and we have cached data
      if (backgroundRefresh && cacheFirst && data.length > 0) {
        // Refresh in background without affecting UI
        setTimeout(async () => {
          try {
            await fetchFromNetwork(query, cacheKey);
          } catch {
            // Silent background refresh failure
          }
        }, 1000);
      }

    } catch (error) {
      console.error('Data loading failed:', error);
    }
  }, [
    query, 
    generateCacheKey, 
    cacheFirst, 
    loadFromCache, 
    staleWhileRevalidate, 
    enableRealtime, 
    setupRealtimeListener, 
    fetchFromNetwork, 
    backgroundRefresh, 
    data.length
  ]);

  // Refresh data manually
  const refresh = useCallback(async () => {
    const cacheKey = queryKeyRef.current;
    if (cacheKey) {
      await fetchFromNetwork(query, cacheKey);
    }
  }, [query, fetchFromNetwork]);

  // Invalidate cache
  const invalidateCache = useCallback(async () => {
    const cacheKey = queryKeyRef.current;
    if (cacheKey) {
      await advancedCache.delete(`firebase:${collectionName}:${cacheKey}`);
      await loadData();
    }
  }, [collectionName, loadData]);

  // Effect to load data
  useEffect(() => {
    loadData();

    // Cleanup
    return () => {
      if (unsubscribeRef.current) {
        unsubscribeRef.current();
        unsubscribeRef.current = null;
      }
    };
  }, [loadData]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (unsubscribeRef.current) {
        unsubscribeRef.current();
      }
    };
  }, []);

  return {
    data,
    loading,
    error,
    metadata,
    refresh,
    invalidateCache,
    // Cache statistics
    cacheStats: cacheUtils.getStats()
  };
}

// Utility hook for cache management
export function useCacheManager() {
  const [stats, setStats] = useState(cacheUtils.getStats());

  const updateStats = useCallback(() => {
    setStats(cacheUtils.getStats());
  }, []);

  const clearCache = useCallback(async () => {
    await cacheUtils.clearAll();
    updateStats();
  }, [updateStats]);

  useEffect(() => {
    const interval = setInterval(updateStats, 5000); // Update every 5 seconds
    return () => clearInterval(interval);
  }, [updateStats]);

  return {
    stats,
    clearCache,
    updateStats
  };
}
