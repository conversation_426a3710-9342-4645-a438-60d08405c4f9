#!/usr/bin/env node

/**
 * Production Health Check Script
 * Validates deployed application health and performance
 */

import https from 'https';
import http from 'http';
import { URL } from 'url';

const HEALTH_CHECKS = [
  {
    name: 'App Loading',
    path: '/',
    expectedStatus: 200,
    expectedContent: ['OSIS', 'React'],
    timeout: 10000
  },
  {
    name: 'Admin Page',
    path: '/admin',
    expectedStatus: 200,
    expectedContent: ['admin', 'login'],
    timeout: 10000
  },
  {
    name: 'Members Page',
    path: '/members',
    expectedStatus: 200,
    expectedContent: ['members', 'anggota'],
    timeout: 10000
  },
  {
    name: 'PWA Manifest',
    path: '/manifest.webmanifest',
    expectedStatus: 200,
    expectedContent: ['OSIS'],
    timeout: 5000
  },
  {
    name: 'Service Worker',
    path: '/sw.js',
    expectedStatus: 200,
    expectedContent: ['workbox', 'precache'],
    timeout: 5000
  }
];

const PERFORMANCE_THRESHOLDS = {
  responseTime: 3000, // 3 seconds
  contentSize: 1024 * 1024 * 2, // 2MB
  compressionRatio: 0.3 // 30% compression minimum
};

function makeRequest(url, timeout = 10000) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const client = urlObj.protocol === 'https:' ? https : http;
    
    const startTime = Date.now();
    
    const req = client.get(url, {
      timeout,
      headers: {
        'User-Agent': 'OSIS-Health-Check/1.0',
        'Accept': 'text/html,application/json,*/*',
        'Accept-Encoding': 'gzip, deflate, br'
      }
    }, (res) => {
      let data = '';
      let rawSize = 0;
      
      res.on('data', (chunk) => {
        data += chunk;
        rawSize += chunk.length;
      });
      
      res.on('end', () => {
        const endTime = Date.now();
        const responseTime = endTime - startTime;
        
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          data,
          responseTime,
          rawSize,
          compressed: res.headers['content-encoding'] ? true : false
        });
      });
    });
    
    req.on('error', reject);
    req.on('timeout', () => {
      req.destroy();
      reject(new Error(`Request timeout after ${timeout}ms`));
    });
  });
}

async function runHealthCheck(baseUrl) {
  console.log(`🏥 Running Health Check for: ${baseUrl}\n`);
  
  let totalChecks = 0;
  let passedChecks = 0;
  let warnings = [];
  let errors = [];

  for (const check of HEALTH_CHECKS) {
    totalChecks++;
    const url = new URL(check.path, baseUrl).toString();
    
    try {
      console.log(`🔍 Checking: ${check.name} (${check.path})`);
      
      const result = await makeRequest(url, check.timeout);
      
      // Status code check
      if (result.statusCode === check.expectedStatus) {
        console.log(`  ✅ Status: ${result.statusCode}`);
      } else {
        console.log(`  ❌ Status: ${result.statusCode} (expected ${check.expectedStatus})`);
        errors.push(`${check.name}: Wrong status code ${result.statusCode}`);
        continue;
      }
      
      // Content check
      let contentFound = true;
      for (const expectedContent of check.expectedContent) {
        if (!result.data.toLowerCase().includes(expectedContent.toLowerCase())) {
          contentFound = false;
          break;
        }
      }
      
      if (contentFound) {
        console.log(`  ✅ Content: Expected content found`);
      } else {
        console.log(`  ⚠️  Content: Some expected content missing`);
        warnings.push(`${check.name}: Expected content not found`);
      }
      
      // Performance check
      if (result.responseTime <= PERFORMANCE_THRESHOLDS.responseTime) {
        console.log(`  ✅ Performance: ${result.responseTime}ms`);
      } else {
        console.log(`  ⚠️  Performance: ${result.responseTime}ms (slow)`);
        warnings.push(`${check.name}: Slow response time ${result.responseTime}ms`);
      }
      
      // Size check
      if (result.rawSize <= PERFORMANCE_THRESHOLDS.contentSize) {
        console.log(`  ✅ Size: ${(result.rawSize / 1024).toFixed(1)}KB`);
      } else {
        console.log(`  ⚠️  Size: ${(result.rawSize / 1024 / 1024).toFixed(1)}MB (large)`);
        warnings.push(`${check.name}: Large content size ${(result.rawSize / 1024 / 1024).toFixed(1)}MB`);
      }
      
      // Compression check
      if (result.compressed) {
        console.log(`  ✅ Compression: Enabled (${result.headers['content-encoding']})`);
      } else {
        console.log(`  ⚠️  Compression: Not detected`);
        warnings.push(`${check.name}: No compression detected`);
      }
      
      passedChecks++;
      console.log('');
      
    } catch (error) {
      console.log(`  ❌ Error: ${error.message}`);
      errors.push(`${check.name}: ${error.message}`);
      console.log('');
    }
  }

  // Security headers check
  console.log('🔒 Security Headers Check:');
  try {
    const result = await makeRequest(baseUrl);
    const headers = result.headers;
    
    const securityHeaders = {
      'x-frame-options': 'X-Frame-Options',
      'x-content-type-options': 'X-Content-Type-Options',
      'x-xss-protection': 'X-XSS-Protection',
      'strict-transport-security': 'Strict-Transport-Security',
      'referrer-policy': 'Referrer-Policy'
    };
    
    for (const [header, displayName] of Object.entries(securityHeaders)) {
      if (headers[header]) {
        console.log(`  ✅ ${displayName}: ${headers[header]}`);
      } else {
        console.log(`  ⚠️  ${displayName}: Missing`);
        warnings.push(`Security: Missing ${displayName} header`);
      }
    }
    
    console.log('');
  } catch (error) {
    console.log(`  ❌ Error checking security headers: ${error.message}\n`);
    errors.push(`Security headers check failed: ${error.message}`);
  }

  // PWA checks
  console.log('📱 PWA Checks:');
  try {
    // Check if HTTPS
    const isHTTPS = baseUrl.startsWith('https://');
    if (isHTTPS) {
      console.log('  ✅ HTTPS: Enabled');
    } else {
      console.log('  ⚠️  HTTPS: Not enabled (required for PWA)');
      warnings.push('PWA: HTTPS not enabled');
    }
    
    // Check manifest
    const manifestResult = await makeRequest(new URL('/manifest.webmanifest', baseUrl).toString());
    if (manifestResult.statusCode === 200) {
      console.log('  ✅ Manifest: Available');
      
      try {
        const manifest = JSON.parse(manifestResult.data);
        if (manifest.name && manifest.short_name && manifest.icons) {
          console.log('  ✅ Manifest: Valid structure');
        } else {
          console.log('  ⚠️  Manifest: Missing required fields');
          warnings.push('PWA: Manifest missing required fields');
        }
      } catch {
        console.log('  ⚠️  Manifest: Invalid JSON');
        warnings.push('PWA: Manifest is not valid JSON');
      }
    } else {
      console.log('  ❌ Manifest: Not found');
      errors.push('PWA: Manifest not found');
    }
    
    console.log('');
  } catch (error) {
    console.log(`  ❌ Error checking PWA: ${error.message}\n`);
    errors.push(`PWA check failed: ${error.message}`);
  }

  // Summary
  console.log('📊 Health Check Summary:');
  console.log(`  Total Checks: ${totalChecks}`);
  console.log(`  Passed: ${passedChecks}`);
  console.log(`  Warnings: ${warnings.length}`);
  console.log(`  Errors: ${errors.length}`);
  console.log('');

  if (warnings.length > 0) {
    console.log('⚠️  Warnings:');
    warnings.forEach(warning => console.log(`  - ${warning}`));
    console.log('');
  }

  if (errors.length > 0) {
    console.log('❌ Errors:');
    errors.forEach(error => console.log(`  - ${error}`));
    console.log('');
  }

  // Final status
  if (errors.length === 0) {
    if (warnings.length === 0) {
      console.log('🎉 Health Check PASSED - All systems operational!');
      process.exit(0);
    } else {
      console.log('⚠️  Health Check PASSED with warnings - Consider addressing warnings');
      process.exit(0);
    }
  } else {
    console.log('❌ Health Check FAILED - Critical issues found');
    process.exit(1);
  }
}

// Main execution
function main() {
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    console.log('Usage: node health-check.js <url>');
    console.log('Example: node health-check.js https://your-app.netlify.app');
    process.exit(1);
  }
  
  const url = args[0];
  
  // Validate URL
  try {
    new URL(url);
  } catch {
    console.log('❌ Invalid URL provided');
    process.exit(1);
  }
  
  runHealthCheck(url);
}

main();
