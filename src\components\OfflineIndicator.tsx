import React, { useState, useEffect } from 'react';
import { WifiOff, Wifi, AlertCircle } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';

const OfflineIndicator: React.FC = () => {
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [showOfflineMessage, setShowOfflineMessage] = useState(false);

  useEffect(() => {
    const handleOnline = () => {
      setIsOnline(true);
      setShowOfflineMessage(false);
    };

    const handleOffline = () => {
      setIsOnline(false);
      setShowOfflineMessage(true);
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // Initial check
    if (!navigator.onLine) {
      setShowOfflineMessage(true);
    }

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  return (
    <>
      {/* Connection Status Indicator (always visible in corner) */}
      <div className="fixed top-4 right-4 z-40">
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          className={`p-2 rounded-full shadow-lg ${
            isOnline 
              ? 'bg-green-500 text-white' 
              : 'bg-red-500 text-white'
          }`}
          title={isOnline ? 'Online' : 'Offline'}
        >
          {isOnline ? <Wifi size={16} /> : <WifiOff size={16} />}
        </motion.div>
      </div>

      {/* Offline Message Banner */}
      <AnimatePresence>
        {showOfflineMessage && (
          <motion.div
            initial={{ opacity: 0, y: -100 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -100 }}
            className="fixed top-0 left-0 right-0 z-50"
          >
            <div className="bg-red-500 text-white px-4 py-3 shadow-lg">
              <div className="flex items-center justify-center space-x-2 max-w-4xl mx-auto">
                <AlertCircle size={20} />
                <span className="text-sm font-medium">
                  You're offline. Some features may be limited.
                </span>
                <button
                  onClick={() => setShowOfflineMessage(false)}
                  className="ml-4 text-red-200 hover:text-white transition-colors"
                >
                  <span className="sr-only">Dismiss</span>
                  ×
                </button>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Back Online Message */}
      <AnimatePresence>
        {isOnline && !showOfflineMessage && (
          <motion.div
            initial={{ opacity: 0, y: -100 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -100 }}
            onAnimationComplete={() => {
              // Auto-hide after 3 seconds
              setTimeout(() => setIsOnline(true), 3000);
            }}
            className="fixed top-0 left-0 right-0 z-50"
          >
            <div className="bg-green-500 text-white px-4 py-3 shadow-lg">
              <div className="flex items-center justify-center space-x-2 max-w-4xl mx-auto">
                <Wifi size={20} />
                <span className="text-sm font-medium">
                  You're back online!
                </span>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
};

export default OfflineIndicator;
