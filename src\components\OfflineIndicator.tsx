import React, { useState, useEffect } from 'react';
import { WifiOff, Wifi } from 'lucide-react';

const OfflineIndicator: React.FC = () => {
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [showOfflineMessage, setShowOfflineMessage] = useState(false);

  useEffect(() => {
    const handleOnline = () => {
      setIsOnline(true);
      setShowOfflineMessage(false);
      // Don't show "back online" message automatically
    };

    const handleOffline = () => {
      setIsOnline(false);
      setShowOfflineMessage(true);
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // Initial check
    if (!navigator.onLine) {
      setShowOfflineMessage(true);
    }

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  return (
    <>
      {/* Simple connection indicator */}
      <div className="fixed top-4 right-4 z-40">
        <div
          className={`p-2 rounded-full shadow-lg ${
            isOnline
              ? 'bg-green-500 text-white'
              : 'bg-red-500 text-white'
          }`}
          title={isOnline ? 'Online' : 'Offline'}
        >
          {isOnline ? <Wifi size={14} /> : <WifiOff size={14} />}
        </div>
      </div>

      {/* Simple offline message */}
      {showOfflineMessage && (
        <div className="fixed top-0 left-0 right-0 z-50">
          <div className="bg-red-500 text-white px-4 py-2 text-center">
            <span className="text-sm">You're offline</span>
            <button
              type="button"
              onClick={() => setShowOfflineMessage(false)}
              className="ml-4 text-red-200 hover:text-white"
            >
              ×
            </button>
          </div>
        </div>
      )}
    </>
  );
};

export default OfflineIndicator;
