/* Styles for AdminPage.tsx */

.menu-icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  border-radius: 9999px; /* rounded-full */
}

.menu-icon {
  width: 22px;
  height: 22px;
}

/* Responsive sizing for the icon container */
.menu-icon-container {
  width: 2.5rem; /* w-10 */
  height: 2.5rem; /* h-10 */
}

@media (min-width: 640px) {
  .menu-icon-container {
    width: 3rem; /* sm:w-12 */
    height: 3rem; /* sm:h-12 */
  }
}

/* Dynamic color classes that will be applied based on the menu item */
.bg-color-members {
  background-color: rgba(157, 224, 210, 0.25); /* #9DE0D240 */
}
.text-color-members {
  color: #9DE0D2;
}

.bg-color-events {
  background-color: rgba(255, 152, 152, 0.25); /* #FF989840 */
}
.text-color-events {
  color: #FF9898;
}

.bg-color-expenses {
  background-color: rgba(252, 224, 155, 0.25); /* #FCE09B40 */
}
.text-color-expenses {
  color: #FCE09B;
}

.bg-color-dues-settings {
  background-color: rgba(179, 157, 219, 0.25); /* #B39DDB40 */
}
.text-color-dues-settings {
  color: #B39DDB;
}
