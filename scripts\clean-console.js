#!/usr/bin/env node

/**
 * Script untuk membersihkan console statements dari production code
 * Jalankan dengan: node scripts/clean-console.js
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Directories to clean
const dirsToClean = [
  path.join(__dirname, '../src'),
];

// Files to exclude from cleaning
const excludeFiles = [
  'errorHandler.ts', // Keep console.error for error handling
  'validate-env.js', // Keep console for CLI script
  'clean-console.js', // Keep console for this script
];

// Console patterns to remove
const consolePatterns = [
  /console\.log\([^)]*\);?\s*$/gm,
  /console\.info\([^)]*\);?\s*$/gm,
  /console\.warn\([^)]*\);?\s*$/gm,
  /console\.debug\([^)]*\);?\s*$/gm,
  /console\.trace\([^)]*\);?\s*$/gm,
];

// Console patterns to keep (for error handling)
const keepPatterns = [
  /console\.error\(/,
];

function shouldCleanFile(filePath) {
  const fileName = path.basename(filePath);
  const ext = path.extname(filePath);
  
  // Only clean TypeScript and JavaScript files
  if (!['.ts', '.tsx', '.js', '.jsx'].includes(ext)) {
    return false;
  }
  
  // Skip excluded files
  if (excludeFiles.some(exclude => fileName.includes(exclude))) {
    return false;
  }
  
  // Skip node_modules and dist
  if (filePath.includes('node_modules') || filePath.includes('dist')) {
    return false;
  }
  
  return true;
}

function cleanConsoleFromFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    let cleanedContent = content;
    let removedCount = 0;
    
    // Split content into lines for better processing
    const lines = content.split('\n');
    const cleanedLines = [];
    
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      let shouldKeepLine = true;
      
      // Check if line contains console statement
      for (const pattern of consolePatterns) {
        if (pattern.test(line)) {
          // Check if it's an error console that should be kept
          const shouldKeep = keepPatterns.some(keepPattern => keepPattern.test(line));
          
          if (!shouldKeep) {
            // Check if it's a standalone console statement or part of larger expression
            const trimmedLine = line.trim();
            if (trimmedLine.startsWith('console.') || 
                trimmedLine.includes('console.log(') ||
                trimmedLine.includes('console.info(') ||
                trimmedLine.includes('console.warn(') ||
                trimmedLine.includes('console.debug(')) {
              shouldKeepLine = false;
              removedCount++;
              break;
            }
          }
        }
      }
      
      if (shouldKeepLine) {
        cleanedLines.push(line);
      }
    }
    
    cleanedContent = cleanedLines.join('\n');
    
    // Only write if content changed
    if (cleanedContent !== content) {
      fs.writeFileSync(filePath, cleanedContent);
      return removedCount;
    }
    
    return 0;
  } catch (error) {
    console.error(`Error cleaning file ${filePath}:`, error.message);
    return 0;
  }
}

function cleanDirectory(dirPath) {
  let totalRemoved = 0;
  let filesProcessed = 0;
  
  function processDirectory(currentPath) {
    const items = fs.readdirSync(currentPath);
    
    for (const item of items) {
      const itemPath = path.join(currentPath, item);
      const stat = fs.statSync(itemPath);
      
      if (stat.isDirectory()) {
        // Skip certain directories
        if (['node_modules', 'dist', '.git'].includes(item)) {
          continue;
        }
        processDirectory(itemPath);
      } else if (stat.isFile() && shouldCleanFile(itemPath)) {
        const removed = cleanConsoleFromFile(itemPath);
        if (removed > 0) {
          console.log(`✅ ${path.relative(process.cwd(), itemPath)}: Removed ${removed} console statements`);
          totalRemoved += removed;
        }
        filesProcessed++;
      }
    }
  }
  
  processDirectory(dirPath);
  return { totalRemoved, filesProcessed };
}

function main() {
  console.log('🧹 Cleaning console statements from production code...\n');
  
  let grandTotalRemoved = 0;
  let grandTotalFiles = 0;
  
  for (const dir of dirsToClean) {
    if (fs.existsSync(dir)) {
      console.log(`📁 Processing directory: ${path.relative(process.cwd(), dir)}`);
      const { totalRemoved, filesProcessed } = cleanDirectory(dir);
      grandTotalRemoved += totalRemoved;
      grandTotalFiles += filesProcessed;
      
      if (totalRemoved > 0) {
        console.log(`   Removed ${totalRemoved} console statements from ${filesProcessed} files\n`);
      } else {
        console.log(`   No console statements found to remove\n`);
      }
    } else {
      console.log(`⚠️  Directory not found: ${dir}\n`);
    }
  }
  
  console.log('📊 Summary:');
  console.log(`   Files processed: ${grandTotalFiles}`);
  console.log(`   Console statements removed: ${grandTotalRemoved}`);
  
  if (grandTotalRemoved > 0) {
    console.log('\n✅ Console cleanup completed successfully!');
    console.log('💡 Note: console.error statements in error handlers were preserved');
  } else {
    console.log('\n✅ No console statements found to remove - code is already clean!');
  }
}

main();
