import { motion } from 'framer-motion';

type LoaderProps = {
  size?: 'small' | 'medium' | 'large';
  variant?: 'default' | 'primary' | 'secondary';
  text?: string;
};

const sizeClasses = {
  small: {
    container: 'h-20 w-20',
    circle: 'h-5 w-5',
  },
  medium: {
    container: 'h-24 w-24',
    circle: 'h-6 w-6',
  },
  large: {
    container: 'h-32 w-32',
    circle: 'h-8 w-8',
  },
};

const variantClasses = {
  default: {
    circle1: 'bg-[#FF9898]',
    circle2: 'bg-[#9DE0D2]',
    circle3: 'bg-[#FCE09B]',
    text: 'text-[#5D534B]',
  },
  primary: {
    circle1: 'bg-[#9DE0D2]',
    circle2: 'bg-[#FF9898]',
    circle3: 'bg-[#9DE0D2]',
    text: 'text-[#5D534B]',
  },
  secondary: {
    circle1: 'bg-[#FCE09B]',
    circle2: 'bg-[#9DE0D2]',
    circle3: 'bg-[#FF9898]',
    text: 'text-[#5D534B]',
  },
};

const Loader = ({ size = 'medium', variant = 'default', text }: LoaderProps) => {
  const containerAnimation = {
    animate: {
      rotate: [0, 360],
      transition: {
        repeat: Infinity,
        duration: 3,
        ease: "linear",
      },
    },
  };

  const circleAnimation = (delay: number) => ({
    animate: {
      scale: [1, 1.3, 1],
      transition: {
        repeat: Infinity,
        duration: 1.5,
        delay: delay,
        ease: "easeInOut",
      },
    },
  });

  const bounceAnimation = {
    animate: {
      y: [0, -10, 0],
      transition: {
        repeat: Infinity,
        duration: 1,
        ease: "easeInOut",
      },
    },
  };

  return (
    <div className="flex flex-col items-center justify-center">
      <div className="relative">
        <motion.div 
          className={`${sizeClasses[size].container} flex items-center justify-center`}
          animate={containerAnimation.animate}
        >
          <motion.div
            className={`absolute ${sizeClasses[size].circle} rounded-full border-4 border-[#5D534B] ${variantClasses[variant].circle1} shadow-[4px_4px_0px_#5D534B]`}
            style={{ top: '10%', left: '50%', marginLeft: '-12px' }}
            animate={circleAnimation(0).animate}
          />
          <motion.div
            className={`absolute ${sizeClasses[size].circle} rounded-full border-4 border-[#5D534B] ${variantClasses[variant].circle2} shadow-[4px_4px_0px_#5D534B]`}
            style={{ bottom: '30%', left: '10%' }}
            animate={circleAnimation(0.5).animate}
          />
          <motion.div
            className={`absolute ${sizeClasses[size].circle} rounded-full border-4 border-[#5D534B] ${variantClasses[variant].circle3} shadow-[4px_4px_0px_#5D534B]`}
            style={{ bottom: '30%', right: '10%' }}
            animate={circleAnimation(1).animate}
          />
        </motion.div>

        {/* Logo in the middle */}
        <motion.div
          className="absolute inset-0 flex items-center justify-center"
          animate={bounceAnimation.animate}
        >
          <div className="bg-white p-2 rounded-full border-4 border-[#5D534B] shadow-[4px_4px_0px_#5D534B]">
            <div className="text-xs font-bold text-[#5D534B]">PSY</div>
          </div>
        </motion.div>
      </div>

      {/* Optional loading text */}
      {text && (
        <motion.div 
          className={`mt-4 font-bold ${variantClasses[variant].text}`}
          initial={{ opacity: 0 }}
          animate={{ opacity: [0.5, 1, 0.5], transition: { repeat: Infinity, duration: 1.5 } }}
        >
          {text}
        </motion.div>
      )}
    </div>
  );
};

export default Loader; 