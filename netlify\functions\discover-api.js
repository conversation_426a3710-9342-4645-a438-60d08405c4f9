const axios = require('axios');

/**
 * Fungsi untuk mencoba berbagai endpoint API dan menemukan yang benar
 */
exports.handler = async function(event, context) {
  const WHATSAPP_API_URL = process.env.WHATSAPP_API_URL || 'https://wabot-production-20ec.up.railway.app';
  const API_KEY = process.env.WHATSAPP_API_KEY || 'pemudapsy2709';
  
  // Daftar endpoint yang akan dicoba
  const endpoints = [
    '/status?sessionId=default',
    '/api/status?sessionId=default',
    '/api/session/default/status',
    '/api/sessions/default/status',
    '/api/session/status?sessionId=default',
    '/api/check-status?sessionId=default',
    '/api/connection-status?sessionId=default'
  ];
  
  const results = {};
  
  for (const endpoint of endpoints) {
    const url = `${WHATSAPP_API_URL}${endpoint}`;
    console.log(`Mencoba endpoint: ${url}`);
    
    try {
      const response = await axios.get(url, {
        headers: {
          'X-API-Key': API_KEY
        }
      });
      
      results[endpoint] = {
        status: response.status,
        success: true,
        data: response.data
      };
    } catch (error) {
      results[endpoint] = {
        status: error.response?.status || 500,
        success: false,
        error: error.message,
        data: error.response?.data
      };
    }
  }
  
  return {
    statusCode: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      baseUrl: WHATSAPP_API_URL,
      results
    })
  };
}; 