import { create } from 'zustand';
import { Transaction, FinanceSummary, transactionAPI } from '../services/api';

interface TransactionStore {
  transactions: Transaction[];
  summary: FinanceSummary | null;
  isLoading: boolean;
  error: string | null;
  fetchTransactions: () => Promise<void>;
  fetchByType: (type: 'income' | 'expense') => Promise<void>;
  fetchSummary: () => Promise<void>;
  addTransaction: (transaction: Omit<Transaction, 'id' | 'created_at'>) => Promise<void>;
  updateTransaction: (id: string, transaction: Partial<Transaction>) => Promise<void>;
  deleteTransaction: (id: string) => Promise<void>;
}

const useTransactionsStore = create<TransactionStore>((set) => ({
  transactions: [],
  summary: null,
  isLoading: false,
  error: null,

  fetchTransactions: async () => {
    set({ isLoading: true, error: null });
    try {
      const transactions = await transactionAPI.getAll();
      set({ transactions, isLoading: false });
    } catch (error) {
      set({ error: (error as Error).message, isLoading: false });
    }
  },

  fetchByType: async (type: 'income' | 'expense') => {
    set({ isLoading: true, error: null });
    try {
      const transactions = await transactionAPI.getByType(type);
      set({ transactions, isLoading: false });
    } catch (error) {
      set({ error: (error as Error).message, isLoading: false });
    }
  },

  fetchSummary: async () => {
    set({ isLoading: true, error: null });
    try {
      const summary = await transactionAPI.getSummary();
      set({ summary, isLoading: false });
    } catch (error) {
      set({ error: (error as Error).message, isLoading: false });
    }
  },

  addTransaction: async (transaction: Omit<Transaction, 'id' | 'created_at'>) => {
    set({ isLoading: true, error: null });
    try {
      const newTransaction = await transactionAPI.create(transaction);
      set((state: TransactionStore) => ({ 
        transactions: [...state.transactions, newTransaction],
        isLoading: false
      }));
      // Update summary after adding a transaction
      const summary = await transactionAPI.getSummary();
      set({ summary });
    } catch (error) {
      set({ error: (error as Error).message, isLoading: false });
    }
  },

  updateTransaction: async (id: string, transaction: Partial<Transaction>) => {
    set({ isLoading: true, error: null });
    try {
      const updatedTransaction = await transactionAPI.update(id, transaction);
      set((state: TransactionStore) => ({
        transactions: state.transactions.map((t: Transaction) => 
          t.id === id ? updatedTransaction : t
        ),
        isLoading: false
      }));
      // Update summary after updating a transaction
      const summary = await transactionAPI.getSummary();
      set({ summary });
    } catch (error) {
      set({ error: (error as Error).message, isLoading: false });
    }
  },

  deleteTransaction: async (id: string) => {
    set({ isLoading: true, error: null });
    try {
      await transactionAPI.delete(id);
      set((state: TransactionStore) => ({
        transactions: state.transactions.filter((t: Transaction) => t.id !== id),
        isLoading: false
      }));
      // Update summary after deleting a transaction
      const summary = await transactionAPI.getSummary();
      set({ summary });
    } catch (error) {
      set({ error: (error as Error).message, isLoading: false });
    }
  }
}));

export default useTransactionsStore; 