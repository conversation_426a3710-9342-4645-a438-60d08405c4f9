import { lazy } from 'react';

// Lazy load heavy UI components to reduce initial bundle size
export const LazyChart = lazy(() => 
  import(/* webpackChunkName: "chart-component" */ './ui/chart').then(module => ({
    default: module.ChartContainer
  }))
);

export const LazyCarousel = lazy(() => 
  import(/* webpackChunkName: "carousel-component" */ './ui/carousel').then(module => ({
    default: module.Carousel
  }))
);

export const LazySidebar = lazy(() => 
  import(/* webpackChunkName: "sidebar-component" */ './ui/sidebar').then(module => ({
    default: module.Sidebar
  }))
);

export const LazyDropdownMenu = lazy(() => 
  import(/* webpackChunkName: "dropdown-component" */ './ui/dropdown-menu').then(module => ({
    default: module.DropdownMenu
  }))
);

export const LazyMenubar = lazy(() => 
  import(/* webpackChunkName: "menubar-component" */ './ui/menubar').then(module => ({
    default: module.Menubar
  }))
);

// Lazy load complex forms
export const LazyMemberForm = lazy(() => 
  import(/* webpackChunkName: "member-form" */ './forms/MemberForm')
);

export const LazyExpenseForm = lazy(() => 
  import(/* webpackChunkName: "expense-form" */ './forms/ExpenseForm')
);

export const LazyEventForm = lazy(() => 
  import(/* webpackChunkName: "event-form" */ './forms/EventForm')
);

// Lazy load admin components
export const LazyAdminStats = lazy(() => 
  import(/* webpackChunkName: "admin-stats" */ './admin/AdminStats')
);

export const LazyAdminTable = lazy(() => 
  import(/* webpackChunkName: "admin-table" */ './admin/AdminTable')
);

// Lazy load data visualization components
export const LazyDataTable = lazy(() => 
  import(/* webpackChunkName: "data-table" */ './DataTable')
);

export const LazyPagination = lazy(() => 
  import(/* webpackChunkName: "pagination" */ './Pagination')
);

// Export all lazy components for easy importing
export const LazyComponents = {
  Chart: LazyChart,
  Carousel: LazyCarousel,
  Sidebar: LazySidebar,
  DropdownMenu: LazyDropdownMenu,
  Menubar: LazyMenubar,
  MemberForm: LazyMemberForm,
  ExpenseForm: LazyExpenseForm,
  EventForm: LazyEventForm,
  AdminStats: LazyAdminStats,
  AdminTable: LazyAdminTable,
  DataTable: LazyDataTable,
  Pagination: LazyPagination
};
