import { lazy } from 'react';

// Lazy load heavy UI components to reduce initial bundle size
export const LazyChart = lazy(() => 
  import(/* webpackChunkName: "chart-component" */ './ui/chart').then(module => ({
    default: module.ChartContainer
  }))
);

export const LazyCarousel = lazy(() => 
  import(/* webpackChunkName: "carousel-component" */ './ui/carousel').then(module => ({
    default: module.Carousel
  }))
);

export const LazySidebar = lazy(() => 
  import(/* webpackChunkName: "sidebar-component" */ './ui/sidebar').then(module => ({
    default: module.Sidebar
  }))
);

export const LazyDropdownMenu = lazy(() => 
  import(/* webpackChunkName: "dropdown-component" */ './ui/dropdown-menu').then(module => ({
    default: module.DropdownMenu
  }))
);

export const LazyMenubar = lazy(() => 
  import(/* webpackChunkName: "menubar-component" */ './ui/menubar').then(module => ({
    default: module.Menubar
  }))
);

// Lazy load admin components that exist
export const LazyMemberForm = lazy(() =>
  import(/* webpackChunkName: "member-form" */ './admin/MemberForm')
);

export const LazyMemberCard = lazy(() =>
  import(/* webpackChunkName: "member-card" */ './admin/MemberCard')
);

export const LazyMemberTable = lazy(() =>
  import(/* webpackChunkName: "member-table" */ './admin/MemberTable')
);

export const LazyMemberSearchFilter = lazy(() =>
  import(/* webpackChunkName: "member-search" */ './admin/MemberSearchFilter')
);

export const LazyPagination = lazy(() => 
  import(/* webpackChunkName: "pagination" */ './Pagination')
);

// Export all lazy components for easy importing
export const LazyComponents = {
  Chart: LazyChart,
  Carousel: LazyCarousel,
  Sidebar: LazySidebar,
  DropdownMenu: LazyDropdownMenu,
  Menubar: LazyMenubar,
  MemberForm: LazyMemberForm,
  MemberCard: LazyMemberCard,
  MemberTable: LazyMemberTable,
  MemberSearchFilter: LazyMemberSearchFilter,
  Pagination: LazyPagination
};
