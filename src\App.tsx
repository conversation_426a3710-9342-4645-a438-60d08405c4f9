import { BrowserRouter } from "react-router-dom";
import { Toaster } from "sonner";
import { AuthProvider } from "@/context/AuthContext";
import AnimatedRoutes from "./components/AnimatedRoutes";
import FirebaseErrorBoundary from "./components/FirebaseErrorBoundary";

const App = () => (
  <>
    <Toaster position="top-right" />
    <FirebaseErrorBoundary>
      <AuthProvider>
        <BrowserRouter>
          <AnimatedRoutes />
        </BrowserRouter>
      </AuthProvider>
    </FirebaseErrorBoundary>
  </>
);

export default App;
