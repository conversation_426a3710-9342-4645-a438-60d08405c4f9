import { BrowserRouter } from "react-router-dom";
import { Toaster } from "sonner";
import { AuthProvider } from "@/context/AuthContext";
import { MembersProvider } from "@/context/MembersContext";
import { EventsProvider } from "@/context/EventsContext";
import { ExpensesProvider } from "@/context/ExpensesContext";
import { DuesConfigProvider } from "@/context/DuesConfigContext";
import AnimatedRoutes from "./components/AnimatedRoutes";
import FirebaseErrorBoundary from "./components/FirebaseErrorBoundary";

const App = () => (
  <>
    <Toaster position="top-right" />
    <FirebaseErrorBoundary>
      <AuthProvider>
        <MembersProvider>
          <EventsProvider>
            <ExpensesProvider>
              <DuesConfigProvider>
                <BrowserRouter>
                  <AnimatedRoutes />
                </BrowserRouter>
              </DuesConfigProvider>
            </ExpensesProvider>
          </EventsProvider>
        </MembersProvider>
      </AuthProvider>
    </FirebaseErrorBoundary>
  </>
);

export default App;
