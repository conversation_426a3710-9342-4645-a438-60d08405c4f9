import { <PERSON><PERSON><PERSON><PERSON>out<PERSON> } from "react-router-dom";
import { Toaster } from "sonner";
import { AuthProvider } from "@/context/AuthContext";
import { MembersProvider } from "@/context/MembersContext";
import { EventsProvider } from "@/context/EventsContext";
import { ExpensesProvider } from "@/context/ExpensesContext";
import { DuesConfigProvider } from "@/context/DuesConfigContext";
import AnimatedRoutes from "./components/AnimatedRoutes";
import FirebaseErrorBoundary from "./components/FirebaseErrorBoundary";
import PWAInstallPrompt from "./components/PWAInstallPrompt";
import PWAUpdateNotification from "./components/PWAUpdateNotification";
import OfflineIndicator from "./components/OfflineIndicator";
import PWAInstallGuide from "./components/PWAInstallGuide";
import PerformanceMonitor from "./components/PerformanceMonitor";
import UpdateManager from "./components/UpdateManager";

const App = () => (
  <>
    <Toaster position="top-right" />
    <FirebaseErrorBoundary>
      <AuthProvider>
        <MembersProvider>
          <EventsProvider>
            <ExpensesProvider>
              <DuesConfigProvider>
                <BrowserRouter>
                  <AnimatedRoutes />
                </BrowserRouter>
              </DuesConfigProvider>
            </ExpensesProvider>
          </EventsProvider>
        </MembersProvider>
      </AuthProvider>
    </FirebaseErrorBoundary>

    {/* PWA Components */}
    <PWAInstallPrompt />
    <PWAUpdateNotification />
    <OfflineIndicator />
    <PWAInstallGuide />
    <PerformanceMonitor />
    <UpdateManager />
  </>
);

export default App;
