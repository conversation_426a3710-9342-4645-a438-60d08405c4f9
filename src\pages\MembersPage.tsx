import { useEffect, useState } from 'react';
import { Users, UserCheck, UserX, User } from 'lucide-react';
import StatCard from '../components/StatCard';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Member } from '../services/api';
import { formatRupiah, formatDate } from '../utils/formatters';
import { Link } from 'react-router-dom';
import Loader from '../components/Loader';
import { useMembersContext } from '../context/MembersContext';
import { StatCardSkeleton, MemberCardSkeleton, EmptyState } from '../components/LoadingStates';

interface MemberSummary {
  totalMembers: number;
  paidMembers: number;
  unpaidMembers: number;
}

const MembersPage = () => {
  const { members, loading } = useMembersContext();
  const [summary, setSummary] = useState<MemberSummary | null>(null);
  const [filter, setFilter] = useState<'all' | 'paid' | 'unpaid'>('all');
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    // Hitung summary dari data members di context
    const paidMembers = members.filter(m => m.payment_status === 'paid').length;
    const unpaidMembers = members.filter(m => m.payment_status === 'unpaid').length;

    setSummary({
      totalMembers: members.length,
      paidMembers: paidMembers,
      unpaidMembers: unpaidMembers
    });
  }, [members, filter]);

  const handleFilterChange = (newFilter: 'all' | 'paid' | 'unpaid') => {
    setFilter(newFilter);
  };

  const filteredMembers = members.filter(member => {
    const matchesSearch = member.name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesFilter = filter === 'all' || member.payment_status === filter;
    return matchesSearch && matchesFilter;
  });

  if (loading) {
    return (
      <div className="bg-[#F9F9F9] text-[#5D534B]">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl md:text-3xl font-bold border-b-4 border-[#9DE0D2] pb-2">Anggota</h1>
        </div>

        {/* Stats skeleton */}
        <div className="grid grid-cols-3 gap-3 mb-4">
          <StatCardSkeleton />
          <StatCardSkeleton />
          <StatCardSkeleton />
        </div>

        {/* Content skeleton */}
        <div className="neo-card p-4">
          <div className="flex items-center mb-4">
            <div className="w-6 h-6 bg-gray-300 rounded mr-2 animate-pulse"></div>
            <div className="h-6 w-32 bg-gray-300 rounded animate-pulse"></div>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {Array.from({ length: 8 }).map((_, i) => (
              <MemberCardSkeleton key={i} />
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-[#F9F9F9] text-[#5D534B]">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl md:text-3xl font-bold border-b-4 border-[#9DE0D2] pb-2">Anggota</h1>
      </div>
      
      {summary && (
        <div className="grid grid-cols-3 gap-3 mb-4">
          <div className="flex items-center p-2 bg-white border-2 border-[#5D534B] rounded-md shadow-[3px_3px_0px_#5D534B]">
            <div className="p-1.5 rounded-full bg-[#FCE09B]/80 flex-shrink-0">
              <Users size={14} className="text-[#5D534B]" />
            </div>
            <div className="ml-2 min-w-0">
              <div className="text-xs text-[#5D534B]/70 truncate">Total</div>
              <div className="text-base font-bold text-[#5D534B]">{summary.totalMembers}</div>
            </div>
          </div>
          
          <div className="flex items-center p-2 bg-white border-2 border-[#5D534B] rounded-md shadow-[3px_3px_0px_#5D534B]">
            <div className="p-1.5 rounded-full bg-[#9DE0D2]/80 flex-shrink-0">
              <UserCheck size={14} className="text-[#5D534B]" />
            </div>
            <div className="ml-2 min-w-0">
              <div className="text-xs text-[#5D534B]/70 truncate">Lunas</div>
              <div className="text-base font-bold text-[#5D534B]">{summary.paidMembers}</div>
            </div>
          </div>
          
          <div className="flex items-center p-2 bg-white border-2 border-[#5D534B] rounded-md shadow-[3px_3px_0px_#5D534B]">
            <div className="p-1.5 rounded-full bg-[#FF9898]/80 flex-shrink-0">
              <UserX size={14} className="text-[#5D534B]" />
            </div>
            <div className="ml-2 min-w-0">
              <div className="text-xs text-[#5D534B]/70 truncate">Belum</div>
              <div className="text-base font-bold text-[#5D534B]">{summary.unpaidMembers}</div>
            </div>
          </div>
        </div>
      )}
      
      <div className="neo-card p-4 overflow-x-auto animate-fade-in">
        <h2 className="text-xl font-bold mb-4 flex items-center text-[#5D534B]">
          <Users className="mr-2" size={20} />
          Daftar Anggota
        </h2>
        
        <div className="flex flex-col md:flex-row justify-between items-center mb-4 gap-3">
          <div className="flex space-x-2">
            <button 
              className={`px-3 py-1 text-sm border-4 border-[#5D534B] rounded-full ${filter === 'all' ? 'bg-[#9DE0D2] text-[#5D534B]' : 'bg-white text-[#5D534B]'}`}
              onClick={() => handleFilterChange('all')}
            >
              Semua
            </button>
            <button 
              className={`px-3 py-1 text-sm border-4 border-[#5D534B] rounded-full ${filter === 'paid' ? 'bg-[#9DE0D2] text-[#5D534B]' : 'bg-white text-[#5D534B]'}`}
              onClick={() => handleFilterChange('paid')}
            >
              Lunas
            </button>
            <button 
              className={`px-3 py-1 text-sm border-4 border-[#5D534B] rounded-full ${filter === 'unpaid' ? 'bg-[#FF9898] text-[#5D534B]' : 'bg-white text-[#5D534B]'}`}
              onClick={() => handleFilterChange('unpaid')}
            >
              Belum Lunas
            </button>
          </div>
          
          <div className="relative w-full md:w-64">
            <input
              type="text"
              placeholder="Cari nama..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full border-4 border-[#5D534B] px-4 py-2 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#9DE0D2] focus:border-[#5D534B]"
            />
          </div>
        </div>
        
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          {filteredMembers.length > 0 ? (
            filteredMembers.map((member) => (
              <div 
                key={member.id} 
                className="bg-white border-4 border-[#5D534B] rounded-xl shadow-[6px_6px_0px_#5D534B] overflow-hidden animate-fade-in"
              >
                <div className="flex justify-between items-center p-3 border-b-2 border-[#5D534B]">
                  <h3 className="text-md font-bold text-[#5D534B] truncate">
                    {member.name}
                  </h3>
                  <span 
                    className={`px-2 py-1 text-xs font-bold border-2 border-[#5D534B] rounded-full ${
                      member.payment_status === 'paid' 
                        ? 'bg-[#9DE0D2] text-[#5D534B]' 
                        : 'bg-[#FF9898] text-[#5D534B]'
                    }`}
                  >
                    {member.payment_status === 'paid' ? 'Lunas' : 'Belum Lunas'}
                  </span>
                </div>
                
                <div className="p-3">
                  <div className="flex justify-between items-center mb-2 text-sm">
                    <span className="text-[#5D534B]">Nominal Iuran:</span>
                    <span className="font-bold text-[#5D534B]">
                      {formatRupiah(member.payment_amount)}
                    </span>
                  </div>
                  
                  {member.payment_date && (
                    <div className="flex justify-between items-center text-sm">
                      <span className="text-[#5D534B]">Tanggal Bayar:</span>
                      <span className="font-bold text-[#5D534B]">
                        {formatDate(member.payment_date)}
                      </span>
                    </div>
                  )}
                </div>
              </div>
            ))
          ) : (
            <div className="col-span-full">
              <EmptyState
                title="Tidak ada anggota ditemukan"
                description={searchTerm ? `Tidak ada anggota dengan nama "${searchTerm}"` : "Belum ada data anggota"}
                icon={<User size={48} className="text-[#5D534B]" />}
              />
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default MembersPage;
