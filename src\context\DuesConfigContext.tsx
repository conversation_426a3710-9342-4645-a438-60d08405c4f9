import React, { createContext, useContext, useState, useEffect, ReactNode, useMemo } from 'react';
import {
  collection,
  addDoc,
  updateDoc,
  doc,
  onSnapshot,
  serverTimestamp,
  query,
  orderBy,
  limit,
  Timestamp
} from 'firebase/firestore';
import { db } from '../lib/firebase';

interface DuesConfigData {
  id: string;
  pemuda_amount: number;
  pemudi_amount: number;
  pelajar_amount: number;
  notes: string;
  createdAt?: Timestamp;
  updatedAt?: Timestamp;
}

interface DuesConfigContextType {
  config: DuesConfigData | null;
  loading: boolean;
  saveConfig: (config: Omit<DuesConfigData, 'id'>) => Promise<void>;
  updateConfig: (id: string, updates: Partial<DuesConfigData>) => Promise<void>;
}

const DuesConfigContext = createContext<DuesConfigContextType | undefined>(undefined);

export const useDuesConfigContext = () => {
  const context = useContext(DuesConfigContext);
  if (!context) {
    throw new Error('useDuesConfigContext must be used within a DuesConfigProvider');
  }
  return context;
};

interface DuesConfigProviderProps {
  children: ReactNode;
}

export const DuesConfigProvider: React.FC<DuesConfigProviderProps> = ({ children }) => {
  const [config, setConfig] = useState<DuesConfigData | null>(null);
  const [loading, setLoading] = useState(true);

  // Optimized collection reference with query
  const configQuery = useMemo(() =>
    query(
      collection(db, 'dues_config'),
      orderBy('updatedAt', 'desc'),
      limit(1) // Only need the latest config
    ),
  []);

  // Load data from Firestore on mount
  useEffect(() => {
    const unsubscribe = onSnapshot(configQuery, (snapshot) => {
      if (!snapshot.empty) {
        // Ambil dokumen pertama (seharusnya hanya ada satu config)
        const doc = snapshot.docs[0];
        setConfig({
          id: doc.id,
          ...doc.data()
        } as DuesConfigData);
      } else {
        // Jika belum ada config, buat default
        createDefaultConfig();
      }
      setLoading(false);
    }, (error) => {
      console.error('Error loading dues config:', error);
      setLoading(false);
    });

    return () => unsubscribe();
  }, [configQuery]);

  const createDefaultConfig = async () => {
    try {
      const defaultConfig = {
        pemuda_amount: 250000,
        pemudi_amount: 150000,
        pelajar_amount: 150000,
        notes: 'Konfigurasi iuran default. Silakan sesuaikan sesuai kebutuhan.',
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      };
      
      await addDoc(collection(db, 'dues_config'), defaultConfig);
    } catch (error) {
      console.error('❌ ERROR CREATING DEFAULT CONFIG:', error);
    }
  };

  const saveConfig = async (configData: Omit<DuesConfigData, 'id'>) => {
    try {
      await addDoc(collection(db, 'dues_config'), {
        ...configData,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      });
    } catch (error) {
      console.error('❌ ERROR SAVING CONFIG:', error);
      throw error;
    }
  };

  const updateConfig = async (id: string, updates: Partial<DuesConfigData>) => {
    try {
      const configDoc = doc(db, 'dues_config', id);
      await updateDoc(configDoc, {
        ...updates,
        updatedAt: serverTimestamp()
      });
    } catch (error) {
      console.error('❌ ERROR UPDATING CONFIG:', error);
      throw error;
    }
  };

  const value: DuesConfigContextType = {
    config,
    loading,
    saveConfig,
    updateConfig,
  };

  return (
    <DuesConfigContext.Provider value={value}>
      {children}
    </DuesConfigContext.Provider>
  );
};

export default DuesConfigContext;
