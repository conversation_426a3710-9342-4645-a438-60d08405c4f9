import React, { createContext, useContext, useState, useEffect, ReactNode, useMemo } from 'react';
import {
  collection,
  addDoc,
  updateDoc,
  deleteDoc,
  doc,
  onSnapshot,
  serverTimestamp,
  query,
  orderBy,
  limit,
  Timestamp
} from 'firebase/firestore';
import { db } from '../lib/firebase';

interface EventData {
  id: string;
  title: string;
  description: string;
  date: string;
  time: string;
  location: string;
  status: 'upcoming' | 'ongoing' | 'completed' | 'cancelled';
  createdBy: string;
  createdAt?: Timestamp;
  updatedAt?: Timestamp;
}

interface EventsContextType {
  events: EventData[];
  loading: boolean;
  addEvent: (event: Omit<EventData, 'id'>) => Promise<void>;
  updateEvent: (id: string, updates: Partial<EventData>) => Promise<void>;
  deleteEvent: (id: string) => Promise<void>;
  getTotalEvents: () => number;
}

const EventsContext = createContext<EventsContextType | undefined>(undefined);

export const useEventsContext = () => {
  const context = useContext(EventsContext);
  if (!context) {
    throw new Error('useEventsContext must be used within an EventsProvider');
  }
  return context;
};

interface EventsProviderProps {
  children: ReactNode;
}

export const EventsProvider: React.FC<EventsProviderProps> = ({ children }) => {
  const [events, setEvents] = useState<EventData[]>([]);
  const [loading, setLoading] = useState(true);

  // Optimized collection reference with query
  const eventsQuery = useMemo(() =>
    query(
      collection(db, 'events'),
      orderBy('date', 'desc'),
      limit(50) // Limit to last 50 events for performance
    ),
  []);

  // Load data from Firestore with optimized query
  useEffect(() => {
    const unsubscribe = onSnapshot(eventsQuery, (snapshot) => {
      const eventsData: EventData[] = [];
      snapshot.forEach((doc) => {
        eventsData.push({
          id: doc.id,
          ...doc.data()
        } as EventData);
      });

      // Sort by date descending (newest first)
      eventsData.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());

      setEvents(eventsData);
      setLoading(false);
    }, (error) => {
      console.error('Error loading events:', error);
      setLoading(false);
    });

    return () => unsubscribe();
  }, [eventsQuery]);

  const addEvent = async (eventData: Omit<EventData, 'id'>) => {
    await addDoc(collection(db, 'events'), {
      ...eventData,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    });
  };

  const updateEvent = async (id: string, updates: Partial<EventData>) => {
    const eventDoc = doc(db, 'events', id);
    await updateDoc(eventDoc, {
      ...updates,
      updatedAt: serverTimestamp()
    });
  };

  const deleteEvent = async (id: string) => {
    const eventDoc = doc(db, 'events', id);
    await deleteDoc(eventDoc);
  };

  const getTotalEvents = () => {
    return events.length;
  };

  const value: EventsContextType = {
    events,
    loading,
    addEvent,
    updateEvent,
    deleteEvent,
    getTotalEvents,
  };

  return (
    <EventsContext.Provider value={value}>
      {children}
    </EventsContext.Provider>
  );
};

export default EventsContext;
