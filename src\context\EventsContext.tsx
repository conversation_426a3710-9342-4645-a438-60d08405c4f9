import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { 
  collection, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  doc, 
  getDocs, 
  onSnapshot,
  serverTimestamp 
} from 'firebase/firestore';
import { db } from '../lib/firebase';

interface EventData {
  id: string;
  title: string;
  description: string;
  date: string;
  time: string;
  location: string;
  status: 'upcoming' | 'ongoing' | 'completed' | 'cancelled';
  createdBy: string;
  createdAt?: any;
  updatedAt?: any;
}

interface EventsContextType {
  events: EventData[];
  loading: boolean;
  addEvent: (event: Omit<EventData, 'id'>) => Promise<void>;
  updateEvent: (id: string, updates: Partial<EventData>) => Promise<void>;
  deleteEvent: (id: string) => Promise<void>;
  getTotalEvents: () => number;
}

const EventsContext = createContext<EventsContextType | undefined>(undefined);

export const useEventsContext = () => {
  const context = useContext(EventsContext);
  if (!context) {
    throw new Error('useEventsContext must be used within an EventsProvider');
  }
  return context;
};

interface EventsProviderProps {
  children: ReactNode;
}

export const EventsProvider: React.FC<EventsProviderProps> = ({ children }) => {
  const [events, setEvents] = useState<EventData[]>([]);
  const [loading, setLoading] = useState(true);

  // Collection reference
  const eventsCollection = collection(db, 'events');

  // Load data from Firestore on mount
  useEffect(() => {
    const unsubscribe = onSnapshot(eventsCollection, (snapshot) => {
      const eventsData: EventData[] = [];
      snapshot.forEach((doc) => {
        eventsData.push({
          id: doc.id,
          ...doc.data()
        } as EventData);
      });
      setEvents(eventsData);
      setLoading(false);
    }, (error) => {
      console.error('Error loading events:', error);
      setLoading(false);
    });

    return () => unsubscribe();
  }, []);

  const addEvent = async (eventData: Omit<EventData, 'id'>) => {
    try {
      const docRef = await addDoc(eventsCollection, {
        ...eventData,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      });
    } catch (error) {
      throw error;
    }
  };

  const updateEvent = async (id: string, updates: Partial<EventData>) => {
    try {
      const eventDoc = doc(db, 'events', id);
      await updateDoc(eventDoc, {
        ...updates,
        updatedAt: serverTimestamp()
      });
    } catch (error) {
      throw error;
    }
  };

  const deleteEvent = async (id: string) => {
    try {
      const eventDoc = doc(db, 'events', id);
      await deleteDoc(eventDoc);
    } catch (error) {
      throw error;
    }
  };

  const getTotalEvents = () => {
    return events.length;
  };

  const value: EventsContextType = {
    events,
    loading,
    addEvent,
    updateEvent,
    deleteEvent,
    getTotalEvents,
  };

  return (
    <EventsContext.Provider value={value}>
      {children}
    </EventsContext.Provider>
  );
};

export default EventsContext;
