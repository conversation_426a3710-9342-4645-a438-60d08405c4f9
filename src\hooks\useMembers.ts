import { useState, useEffect, useMemo, useCallback } from 'react';
import { 
  collection, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  doc, 
  getDocs, 
  onSnapshot,
  serverTimestamp,
  query,
  orderBy,
  limit
} from 'firebase/firestore';
import { db } from '../lib/firebase';

export interface MemberData {
  id: string;
  name: string;
  payment_status: 'paid' | 'unpaid';
  payment_amount: number;
  payment_date: string | null;
}

export const useMembers = () => {
  const [allMembers, setAllMembers] = useState<MemberData[]>([]);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPageState] = useState(12);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterStatus, setFilterStatus] = useState<'all' | 'paid' | 'unpaid'>('all');

  // Optimized collection reference with query
  const membersQuery = useMemo(() => 
    query(
      collection(db, 'members'),
      orderBy('name', 'asc'),
      limit(200) // Reasonable limit for performance
    ), 
  []);

  // Load data from Firestore with optimized query
  useEffect(() => {
    let unsubscribe: (() => void) | null = null;

    const loadMembers = async () => {
      try {
        // First try to get data directly with timeout (better for Chrome)
        const timeoutPromise = new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Firebase timeout')), 10000)
        );

        const snapshot = await Promise.race([
          getDocs(membersQuery),
          timeoutPromise
        ]) as Awaited<ReturnType<typeof getDocs>>;
        const membersData: MemberData[] = [];
        snapshot.forEach((doc: any) => {
          membersData.push({
            id: doc.id,
            ...doc.data()
          } as MemberData);
        });

        setAllMembers(membersData);
        setLoading(false);

        // Then set up real-time listener with optimized query
        unsubscribe = onSnapshot(membersQuery, (snapshot) => {
          const updatedMembersData: MemberData[] = [];
          snapshot.forEach((doc) => {
            updatedMembersData.push({
              id: doc.id,
              ...doc.data()
            } as MemberData);
          });
          setAllMembers(updatedMembersData);
        }, (error) => {
          // Don't set loading to false here, keep the initial data
          console.error('Members listener error:', error);
        });

      } catch (error) {
        setLoading(false);

        // Show user-friendly error
        if (typeof window !== 'undefined') {
          // Check if it's a permissions error
          if (error instanceof Error && error.message.includes('Missing or insufficient permissions')) {
            console.error('🔐 Firebase Rules Error: Database rules need to be configured');
            console.error('📋 Solution: Update Firestore Rules to allow read/write access');
          }
        }
      }
    };

    loadMembers();

    return () => {
      if (unsubscribe) {
        unsubscribe();
      }
    };
  }, [membersQuery]);

  // Filter and search logic
  const filteredMembers = useMemo(() => {
    let filtered = allMembers;

    // Apply search filter
    if (searchQuery.trim()) {
      filtered = filtered.filter(member =>
        member.name.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Apply status filter
    if (filterStatus !== 'all') {
      filtered = filtered.filter(member => member.payment_status === filterStatus);
    }

    return filtered;
  }, [allMembers, searchQuery, filterStatus]);

  // Pagination logic
  const totalPages = Math.ceil(filteredMembers.length / itemsPerPage);
  const members = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return filteredMembers.slice(startIndex, endIndex);
  }, [filteredMembers, currentPage, itemsPerPage]);

  // CRUD operations
  const addMember = useCallback(async (memberData: Omit<MemberData, 'id'>) => {
    await addDoc(collection(db, 'members'), {
      ...memberData,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    });
  }, []);

  const updateMember = useCallback(async (id: string, memberData: Partial<MemberData>) => {
    const memberRef = doc(db, 'members', id);
    await updateDoc(memberRef, {
      ...memberData,
      updatedAt: serverTimestamp()
    });
  }, []);

  const deleteMember = useCallback(async (id: string) => {
    const memberRef = doc(db, 'members', id);
    await deleteDoc(memberRef);
  }, []);

  const toggleMemberStatus = useCallback(async (id: string) => {
    const member = allMembers.find(m => m.id === id);
    if (!member) return;

    const newStatus = member.payment_status === 'paid' ? 'unpaid' : 'paid';
    await updateMember(id, { payment_status: newStatus });
  }, [allMembers, updateMember]);

  // Helper functions
  const setPage = useCallback((page: number) => {
    setCurrentPage(page);
  }, []);

  const searchMembers = useCallback((query: string) => {
    setSearchQuery(query);
    setCurrentPage(1); // Reset to first page when searching
  }, []);

  const filterMembers = useCallback((status: 'all' | 'paid' | 'unpaid') => {
    setFilterStatus(status);
    setCurrentPage(1); // Reset to first page when filtering
  }, []);

  const setItemsPerPage = useCallback((items: number) => {
    setItemsPerPageState(items);
    setCurrentPage(1); // Reset to first page when changing items per page
  }, []);

  // Statistics
  const totalMembers = filteredMembers.length;
  const paidMembers = allMembers.filter(m => m.payment_status === 'paid').length;
  const unpaidMembers = allMembers.filter(m => m.payment_status === 'unpaid').length;
  const totalRevenue = allMembers
    .filter(m => m.payment_status === 'paid')
    .reduce((sum, m) => sum + m.payment_amount, 0);

  return {
    // Data
    members,
    allMembers,
    loading,
    
    // Pagination
    currentPage,
    totalPages,
    itemsPerPage,
    totalMembers,
    
    // Statistics
    paidMembers,
    unpaidMembers,
    totalRevenue,
    
    // CRUD operations
    addMember,
    updateMember,
    deleteMember,
    toggleMemberStatus,
    
    // Helper functions
    setPage,
    searchMembers,
    filterMembers,
    setItemsPerPage
  };
};
