import { useState, useMemo, useCallback } from 'react';
import {
  collection,
  addDoc,
  updateDoc,
  deleteDoc,
  doc,
  serverTimestamp,
  query,
  orderBy,
  limit
} from 'firebase/firestore';
import { db } from '../lib/firebase';
import { useSmartFirebaseCache } from './useSmartFirebaseCache';

export interface MemberData {
  id: string;
  name: string;
  payment_status: 'paid' | 'unpaid';
  payment_amount: number;
  payment_date: string | null;
}

export const useMembers = () => {
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPageState] = useState(12);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterStatus, setFilterStatus] = useState<'all' | 'paid' | 'unpaid'>('all');

  // Optimized collection reference with query
  const membersQuery = useMemo(() =>
    query(
      collection(db, 'members'),
      orderBy('name', 'asc'),
      limit(200) // Reasonable limit for performance
    ),
  []);

  // Use smart Firebase cache with advanced caching strategy
  const {
    data: allMembers,
    loading,
    refresh: refreshMembers,
    invalidateCache
  } = useSmartFirebaseCache(membersQuery, 'members', {
    enableRealtime: true,
    cacheFirst: true,
    backgroundRefresh: true,
    staleWhileRevalidate: true,
    maxAge: 60 * 60 * 1000, // 1 hour cache
    retryAttempts: 3,
    onError: (error) => {
      console.error('Members cache error:', error);
      // Show user-friendly error
      if (error.message.includes('Missing or insufficient permissions')) {
        console.error('🔐 Firebase Rules Error: Database rules need to be configured');
        console.error('📋 Solution: Update Firestore Rules to allow read/write access');
      }
    }
  });

  // Filter and search logic
  const filteredMembers = useMemo(() => {
    let filtered = allMembers;

    // Apply search filter
    if (searchQuery.trim()) {
      filtered = filtered.filter(member =>
        member.name.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Apply status filter
    if (filterStatus !== 'all') {
      filtered = filtered.filter(member => member.payment_status === filterStatus);
    }

    return filtered;
  }, [allMembers, searchQuery, filterStatus]);

  // Pagination logic
  const totalPages = Math.ceil(filteredMembers.length / itemsPerPage);
  const members = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return filteredMembers.slice(startIndex, endIndex);
  }, [filteredMembers, currentPage, itemsPerPage]);

  // CRUD operations
  const addMember = useCallback(async (memberData: Omit<MemberData, 'id'>) => {
    await addDoc(collection(db, 'members'), {
      ...memberData,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    });
  }, []);

  const updateMember = useCallback(async (id: string, memberData: Partial<MemberData>) => {
    const memberRef = doc(db, 'members', id);
    await updateDoc(memberRef, {
      ...memberData,
      updatedAt: serverTimestamp()
    });
  }, []);

  const deleteMember = useCallback(async (id: string) => {
    const memberRef = doc(db, 'members', id);
    await deleteDoc(memberRef);
  }, []);

  const toggleMemberStatus = useCallback(async (id: string) => {
    const member = allMembers.find(m => m.id === id);
    if (!member) return;

    const newStatus = member.payment_status === 'paid' ? 'unpaid' : 'paid';
    await updateMember(id, { payment_status: newStatus });
  }, [allMembers, updateMember]);

  // Helper functions
  const setPage = useCallback((page: number) => {
    setCurrentPage(page);
  }, []);

  const searchMembers = useCallback((query: string) => {
    setSearchQuery(query);
    setCurrentPage(1); // Reset to first page when searching
  }, []);

  const filterMembers = useCallback((status: 'all' | 'paid' | 'unpaid') => {
    setFilterStatus(status);
    setCurrentPage(1); // Reset to first page when filtering
  }, []);

  const setItemsPerPage = useCallback((items: number) => {
    setItemsPerPageState(items);
    setCurrentPage(1); // Reset to first page when changing items per page
  }, []);

  // Statistics
  const totalMembers = filteredMembers.length;
  const paidMembers = allMembers.filter(m => m.payment_status === 'paid').length;
  const unpaidMembers = allMembers.filter(m => m.payment_status === 'unpaid').length;
  const totalRevenue = allMembers
    .filter(m => m.payment_status === 'paid')
    .reduce((sum, m) => sum + m.payment_amount, 0);

  return {
    // Data
    members,
    allMembers,
    loading,

    // Pagination
    currentPage,
    totalPages,
    itemsPerPage,
    totalMembers,

    // Statistics
    paidMembers,
    unpaidMembers,
    totalRevenue,

    // CRUD operations
    addMember,
    updateMember,
    deleteMember,
    toggleMemberStatus,

    // Helper functions
    setPage,
    searchMembers,
    filterMembers,
    setItemsPerPage,

    // Cache functions
    refreshMembers,
    invalidateCache
  };
};
