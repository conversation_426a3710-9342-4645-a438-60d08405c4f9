import React, { useState, useEffect } from 'react';
import { Download, Refresh<PERSON><PERSON>, Bell, CheckCircle, AlertCircle } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';

interface UpdateInfo {
  version: string;
  releaseDate: string;
  features: string[];
  fixes: string[];
  size: string;
}

const UpdateManager: React.FC = () => {
  const [updateAvailable, setUpdateAvailable] = useState(false);
  const [updateInfo, setUpdateInfo] = useState<UpdateInfo | null>(null);
  const [isUpdating, setIsUpdating] = useState(false);
  const [updateProgress, setUpdateProgress] = useState(0);
  const [showUpdateDetails, setShowUpdateDetails] = useState(false);
  const [lastChecked, setLastChecked] = useState<Date | null>(null);

  useEffect(() => {
    // Check for updates on app start
    checkForUpdates();

    // Set up periodic update checks (every 30 minutes)
    const updateInterval = setInterval(checkForUpdates, 30 * 60 * 1000);

    // Listen for service worker updates
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.addEventListener('message', handleServiceWorkerMessage);
    }

    return () => {
      clearInterval(updateInterval);
      if ('serviceWorker' in navigator) {
        navigator.serviceWorker.removeEventListener('message', handleServiceWorkerMessage);
      }
    };
  }, []);

  const handleServiceWorkerMessage = (event: MessageEvent) => {
    if (event.data && event.data.type === 'UPDATE_AVAILABLE') {
      setUpdateAvailable(true);
      setUpdateInfo(event.data.updateInfo || getDefaultUpdateInfo());
    }
  };

  const checkForUpdates = async () => {
    try {
      setLastChecked(new Date());
      
      // Simulate update check (in real app, this would call your API)
      const hasUpdate = Math.random() > 0.8; // 20% chance for demo
      
      if (hasUpdate) {
        setUpdateAvailable(true);
        setUpdateInfo(getDefaultUpdateInfo());
      }
    } catch (error) {
      console.error('Error checking for updates:', error);
    }
  };

  const getDefaultUpdateInfo = (): UpdateInfo => ({
    version: '2.1.0',
    releaseDate: new Date().toLocaleDateString('id-ID'),
    features: [
      'Improved offline functionality',
      'Faster data synchronization',
      'Enhanced user interface',
      'Better performance monitoring'
    ],
    fixes: [
      'Fixed member data sync issues',
      'Resolved offline mode bugs',
      'Improved app stability',
      'Fixed notification display'
    ],
    size: '2.3 MB'
  });

  const handleUpdate = async () => {
    setIsUpdating(true);
    setUpdateProgress(0);

    try {
      // Simulate update progress
      const progressInterval = setInterval(() => {
        setUpdateProgress(prev => {
          if (prev >= 100) {
            clearInterval(progressInterval);
            return 100;
          }
          return prev + 10;
        });
      }, 200);

      // Wait for progress to complete
      await new Promise(resolve => {
        const checkProgress = () => {
          if (updateProgress >= 100) {
            resolve(void 0);
          } else {
            setTimeout(checkProgress, 100);
          }
        };
        checkProgress();
      });

      // Apply update (reload page)
      setTimeout(() => {
        window.location.reload();
      }, 1000);

    } catch (error) {
      console.error('Error updating app:', error);
      setIsUpdating(false);
      setUpdateProgress(0);
    }
  };

  const dismissUpdate = () => {
    setUpdateAvailable(false);
    setShowUpdateDetails(false);
    // Remember dismissal for this session
    sessionStorage.setItem('update-dismissed', 'true');
  };

  const formatTimeAgo = (date: Date): string => {
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    
    if (diffMins < 1) return 'Baru saja';
    if (diffMins < 60) return `${diffMins} menit yang lalu`;
    
    const diffHours = Math.floor(diffMins / 60);
    if (diffHours < 24) return `${diffHours} jam yang lalu`;
    
    const diffDays = Math.floor(diffHours / 24);
    return `${diffDays} hari yang lalu`;
  };

  // Don't show if dismissed this session
  if (sessionStorage.getItem('update-dismissed')) {
    return null;
  }

  return (
    <>
      {/* Update Available Notification */}
      <AnimatePresence>
        {updateAvailable && !isUpdating && (
          <motion.div
            initial={{ opacity: 0, y: -100 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -100 }}
            className="fixed top-4 left-4 right-4 md:left-auto md:right-4 md:max-w-sm z-50"
          >
            <div className="bg-blue-50 border border-blue-200 rounded-lg shadow-lg p-4">
              <div className="flex items-start justify-between mb-3">
                <div className="flex items-center space-x-2">
                  <div className="p-2 bg-blue-500 rounded-lg">
                    <Bell size={20} className="text-white" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-blue-900 text-sm">Update Available</h3>
                    <p className="text-xs text-blue-700">
                      OSIS v{updateInfo?.version} is ready
                    </p>
                  </div>
                </div>
                <button
                  type="button"
                  onClick={dismissUpdate}
                  className="text-blue-400 hover:text-blue-600 transition-colors"
                  title="Dismiss update"
                >
                  ✕
                </button>
              </div>

              <div className="flex space-x-2">
                <button
                  type="button"
                  onClick={() => setShowUpdateDetails(true)}
                  className="flex-1 bg-blue-100 text-blue-700 px-3 py-2 rounded-lg text-sm font-medium hover:bg-blue-200 transition-colors"
                >
                  Details
                </button>
                <button
                  type="button"
                  onClick={handleUpdate}
                  className="flex-1 bg-blue-500 text-white px-3 py-2 rounded-lg text-sm font-medium hover:bg-blue-600 transition-colors flex items-center justify-center space-x-1"
                >
                  <Download size={16} />
                  <span>Update</span>
                </button>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Update Progress */}
      <AnimatePresence>
        {isUpdating && (
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.9 }}
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50"
          >
            <div className="bg-white rounded-lg shadow-xl p-6 max-w-sm w-full">
              <div className="text-center">
                <div className="mb-4">
                  <RefreshCw size={48} className="text-blue-500 mx-auto animate-spin" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Updating OSIS</h3>
                <p className="text-sm text-gray-600 mb-4">
                  Please wait while we update the app...
                </p>
                
                {/* Progress Bar */}
                <div className="w-full bg-gray-200 rounded-full h-2 mb-2">
                  <div 
                    className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${updateProgress}%` }}
                  ></div>
                </div>
                <p className="text-xs text-gray-500">{updateProgress}% complete</p>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Update Details Modal */}
      <AnimatePresence>
        {showUpdateDetails && updateInfo && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50"
            onClick={() => setShowUpdateDetails(false)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-white rounded-lg shadow-xl max-w-md w-full max-h-[80vh] overflow-y-auto"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-xl font-bold text-gray-900">Update Details</h2>
                  <button
                    type="button"
                    onClick={() => setShowUpdateDetails(false)}
                    className="text-gray-400 hover:text-gray-600 transition-colors"
                  >
                    ✕
                  </button>
                </div>

                <div className="space-y-4">
                  {/* Version Info */}
                  <div className="bg-blue-50 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <span className="font-semibold text-blue-900">Version {updateInfo.version}</span>
                      <span className="text-sm text-blue-700">{updateInfo.size}</span>
                    </div>
                    <p className="text-sm text-blue-700">Released: {updateInfo.releaseDate}</p>
                  </div>

                  {/* New Features */}
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-2 flex items-center space-x-2">
                      <CheckCircle size={16} className="text-green-500" />
                      <span>New Features</span>
                    </h3>
                    <ul className="space-y-1">
                      {updateInfo.features.map((feature, index) => (
                        <li key={index} className="text-sm text-gray-600 flex items-start space-x-2">
                          <span className="text-green-500 mt-1">•</span>
                          <span>{feature}</span>
                        </li>
                      ))}
                    </ul>
                  </div>

                  {/* Bug Fixes */}
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-2 flex items-center space-x-2">
                      <AlertCircle size={16} className="text-orange-500" />
                      <span>Bug Fixes</span>
                    </h3>
                    <ul className="space-y-1">
                      {updateInfo.fixes.map((fix, index) => (
                        <li key={index} className="text-sm text-gray-600 flex items-start space-x-2">
                          <span className="text-orange-500 mt-1">•</span>
                          <span>{fix}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>

                <div className="mt-6 flex space-x-3">
                  <button
                    type="button"
                    onClick={() => setShowUpdateDetails(false)}
                    className="flex-1 px-4 py-2 text-gray-600 text-sm font-medium hover:text-gray-800 transition-colors"
                  >
                    Close
                  </button>
                  <button
                    type="button"
                    onClick={() => {
                      setShowUpdateDetails(false);
                      handleUpdate();
                    }}
                    className="flex-1 bg-blue-500 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-blue-600 transition-colors flex items-center justify-center space-x-2"
                  >
                    <Download size={16} />
                    <span>Update Now</span>
                  </button>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Last Checked Indicator */}
      {lastChecked && (
        <div className="fixed bottom-4 right-20 text-xs text-gray-500 bg-white px-2 py-1 rounded shadow-sm">
          Last checked: {formatTimeAgo(lastChecked)}
        </div>
      )}
    </>
  );
};

export default UpdateManager;
