#!/usr/bin/env node

/**
 * Add Sample Data to Firebase Firestore
 * Run this script to populate the database with sample members data
 */

const { initializeApp } = require('firebase/app');
const { getFirestore, collection, addDoc, getDocs } = require('firebase/firestore');
require('dotenv').config();

// Firebase configuration
const firebaseConfig = {
  apiKey: process.env.VITE_FIREBASE_API_KEY,
  authDomain: process.env.VITE_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.VITE_FIREBASE_PROJECT_ID,
  storageBucket: process.env.VITE_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.VITE_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.VITE_FIREBASE_APP_ID
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);

// Sample members data
const sampleMembers = [
  {
    name: '<PERSON>',
    payment_status: 'paid',
    payment_amount: 50000,
    payment_date: '2024-01-15'
  },
  {
    name: 'Sit<PERSON>za',
    payment_status: 'paid',
    payment_amount: 50000,
    payment_date: '2024-01-16'
  },
  {
    name: 'Budi Santoso',
    payment_status: 'unpaid',
    payment_amount: 50000,
    payment_date: null
  },
  {
    name: 'Dewi Sartika',
    payment_status: 'paid',
    payment_amount: 50000,
    payment_date: '2024-01-18'
  },
  {
    name: 'Eko Prasetyo',
    payment_status: 'unpaid',
    payment_amount: 50000,
    payment_date: null
  },
  {
    name: 'Fitri Handayani',
    payment_status: 'paid',
    payment_amount: 50000,
    payment_date: '2024-01-20'
  },
  {
    name: 'Gilang Ramadhan',
    payment_status: 'unpaid',
    payment_amount: 50000,
    payment_date: null
  },
  {
    name: 'Hana Pertiwi',
    payment_status: 'paid',
    payment_amount: 50000,
    payment_date: '2024-01-22'
  }
];

async function addSampleData() {
  try {
    console.log('🔥 Connecting to Firebase...');
    
    // Check if members collection already has data
    const membersSnapshot = await getDocs(collection(db, 'members'));
    
    if (!membersSnapshot.empty) {
      console.log('📊 Members collection already has data:', membersSnapshot.size, 'members');
      console.log('✅ No need to add sample data');
      return;
    }
    
    console.log('📝 Adding sample members data...');
    
    // Add sample members
    for (const member of sampleMembers) {
      const docRef = await addDoc(collection(db, 'members'), {
        ...member,
        createdAt: new Date(),
        updatedAt: new Date()
      });
      console.log('✅ Added member:', member.name, '(ID:', docRef.id + ')');
    }
    
    console.log('🎉 Sample data added successfully!');
    console.log('📊 Total members added:', sampleMembers.length);
    
    // Summary
    const paidMembers = sampleMembers.filter(m => m.payment_status === 'paid').length;
    const unpaidMembers = sampleMembers.filter(m => m.payment_status === 'unpaid').length;
    
    console.log('📈 Summary:');
    console.log('  - Paid members:', paidMembers);
    console.log('  - Unpaid members:', unpaidMembers);
    console.log('  - Total revenue:', paidMembers * 50000, 'IDR');
    
  } catch (error) {
    console.error('❌ Error adding sample data:', error);
    process.exit(1);
  }
}

// Run the script
addSampleData();
