const axios = require('axios');

/**
 * Netlify Function yang berfungsi sebagai proxy CORS untuk WhatsApp API
 * Ini akan meneruskan semua request dari frontend ke WhatsApp API di Railway
 * dan mengembalikan responsnya ke frontend dengan header CORS yang benar
 */
exports.handler = async function(event, context) {
  // URL WhatsApp API di Railway
  const WHATSAPP_API_URL = process.env.WHATSAPP_API_URL || 'https://wabot-production-20ec.up.railway.app';
  
  // API Key untuk WhatsApp API
  const WHATSAPP_API_KEY = process.env.WHATSAPP_API_KEY || 'pemudapsy2709';
  
  // Cek jika ini adalah OPTIONS request (CORS preflight)
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type, X-API-Key',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ status: true })
    };
  }

  // Log untuk debugging
  console.log('Path dari client:', event.path);
  console.log('HTTP Method:', event.httpMethod);
  console.log('Headers:', JSON.stringify(event.headers));
  console.log('Query String Parameters:', JSON.stringify(event.queryStringParameters));
  
  try {
    const path = new URL(event.rawUrl).pathname.replace('/.netlify/functions/whatsapp-proxy', '');
    console.log('Path permintaan:', path);
    
    // Transformasi path jika diperlukan
    let apiPath = path;
    if (path === '/') {
      apiPath = '/api/info';
      console.log(`Path diubah melalui mapping: ${path} => ${apiPath}`);
    }

    // Mapping API paths untuk endpoint groups
    if (path.startsWith('/api/groups')) {
      const url = new URL(event.rawUrl);
      const sessionId = url.searchParams.get('sessionId') || 'default';
      apiPath = `/api/sessions/${sessionId}/groups`;
      console.log(`Remapped groups path to: ${apiPath}`);
    }
    
    // Mapping untuk endpoint status bot
    if (path.includes('/status')) {
      const url = new URL(event.rawUrl);
      const sessionId = url.searchParams.get('sessionId') || 'default';
      apiPath = `/api/session/${sessionId}/status`;
      console.log(`Remapped status path to: ${apiPath}`);
    }
    
    // URL lengkap ke WhatsApp API
    const apiUrl = `${WHATSAPP_API_URL}${apiPath}`;
    console.log('Meneruskan request ke URL:', apiUrl);
    
    // Headers untuk request ke WhatsApp API
    const headers = {
      'Content-Type': 'application/json',
      'X-API-Key': event.headers['x-api-key'] || WHATSAPP_API_KEY
    };
    
    // Log semua headers untuk debugging
    console.log('Headers request ke API:', JSON.stringify(headers));
    
    // Body request (jika ada)
    const body = event.body ? JSON.parse(event.body) : undefined;
    if (body) {
      console.log('Body request ke API:', JSON.stringify(body));
    }
    
    // Kirim request ke WhatsApp API
    const response = await axios({
      method: event.httpMethod,
      url: apiUrl,
      headers: headers,
      data: body,
      timeout: 30000 // Tambahkan timeout 30 detik
    });
    
    // Kembalikan response dari WhatsApp API ke frontend
    return {
      statusCode: 200,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type, X-API-Key',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(response.data)
    };
  } catch (error) {
    console.error('Proxy error pada path:', event.path);
    console.error('Error detail:', error.message);
    console.error('Response error:', error.response?.data);
    
    // Cek jika error adalah timeout
    if (error.code === 'ECONNABORTED') {
      return {
        statusCode: 504,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Headers': 'Content-Type, X-API-Key',
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          status: false,
          message: 'Request timeout - WhatsApp API tidak merespons',
          path: event.path,
          data: {
            status: 'error',
            message: 'Timeout waiting for WhatsApp API response',
            timestamp: new Date().toISOString()
          }
        })
      };
    }
    
    // Cek jika error adalah connection refused
    if (error.code === 'ECONNREFUSED') {
      return {
        statusCode: 503,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Headers': 'Content-Type, X-API-Key',
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          status: false,
          message: 'WhatsApp API tidak dapat diakses',
          path: event.path,
          data: {
            status: 'error',
            message: 'Cannot connect to WhatsApp API',
            timestamp: new Date().toISOString()
          }
        })
      };
    }
    
    // Kembalikan error ke frontend
    return {
      statusCode: error.response?.status || 500,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type, X-API-Key',
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        status: false,
        message: `Error: ${error.message}`,
        path: event.path,
        data: error.response?.data || {
          status: 'error',
          message: error.message,
          timestamp: new Date().toISOString()
        }
      })
    };
  }
}; 