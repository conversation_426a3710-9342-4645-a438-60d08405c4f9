// Page transition variants for framer-motion
export const pageTransition = {
  initial: { opacity: 0, y: 20 },
  animate: { opacity: 1, y: 0, transition: { duration: 0.5 } },
  exit: { opacity: 0, y: -20, transition: { duration: 0.3 } }
};

// Nav link variants for staggered animations
export const navLinkVariants = {
  initial: { opacity: 0, y: -10 },
  animate: (i: number) => ({ 
    opacity: 1, 
    y: 0, 
    transition: { 
      delay: i * 0.1 + 0.3,
      duration: 0.5
    } 
  }),
  exit: { opacity: 0, y: 10 }
};

// Card variants for staggered animations
export const cardVariants = {
  initial: { opacity: 0, y: 20 },
  animate: (i: number) => ({ 
    opacity: 1, 
    y: 0, 
    transition: { 
      delay: i * 0.1,
      duration: 0.5 
    } 
  }),
  exit: { opacity: 0, scale: 0.95 }
};

// Button hover animations
export const buttonHoverVariants = {
  initial: { scale: 1 },
  hover: { scale: 1.05 },
  tap: { scale: 0.95 }
}; 