export interface Transaction {
  id: string;
  description: string;
  amount: number;
  date: string;
  type: 'income' | 'expense';
  notes?: string;
  created_at?: string;
}

export interface Member {
  id: string;
  name: string;
  paymentStatus: 'paid' | 'unpaid';
  paymentAmount: number;
  paymentDate: string | null;
  created_at?: string;
}

export interface Event {
  id: string;
  title: string;
  description: string;
  date: string;
  time: string;
  location: string;
  created_at?: string;
}

export interface FinanceSummary {
  totalIncome: number;
  totalExpense: number;
  balance: number;
}

export interface MemberSummary {
  totalMembers: number;
  paidMembers: number;
  unpaidMembers: number;
}
