# 🚀 Production Deployment Guide

## 🎯 Overview

Panduan lengkap untuk deploy aplikasi OSIS ke production dengan CI/CD pipeline, monitoring, dan optimasi performa.

## 🏗️ Deployment Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    OSIS Deployment Flow                    │
├─────────────────────────────────────────────────────────────┤
│ GitHub → GitHub Actions → Build → Test → Deploy → Monitor  │
│    ↓           ↓            ↓       ↓       ↓        ↓     │
│  Code      CI/CD Pipeline  Dist   QA    Netlify  Analytics │
└─────────────────────────────────────────────────────────────┘
```

## 🌐 Deployment Platforms

### 1. **Netlify** (Recommended)
- **Pros**: Excellent PWA support, edge functions, form handling
- **Cons**: Limited server-side capabilities
- **Best for**: Static sites, PWAs, JAMstack apps

### 2. **Vercel** (Alternative)
- **Pros**: Great performance, edge functions, analytics
- **Cons**: More expensive for high traffic
- **Best for**: React apps, serverless functions

### 3. **Cloudflare Pages** (Budget Option)
- **Pros**: Free tier, global CDN, great performance
- **Cons**: Limited build time, fewer features
- **Best for**: Static sites, cost-conscious deployments

## 🔧 Pre-Deployment Setup

### 📋 Prerequisites

1. **Firebase Project** (Production)
2. **GitHub Repository**
3. **Deployment Platform Account** (Netlify/Vercel)
4. **Domain Name** (Optional)

### 🔑 Environment Variables Setup

#### Required Variables:
```bash
VITE_FIREBASE_API_KEY=your_production_api_key
VITE_FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=your_project_id
VITE_FIREBASE_STORAGE_BUCKET=your_project.appspot.com
VITE_FIREBASE_MESSAGING_SENDER_ID=************
VITE_FIREBASE_APP_ID=1:************:web:abcdef1234567890
```

#### Optional Variables:
```bash
VITE_APP_NAME=OSIS
VITE_APP_VERSION=1.0.0
VITE_APP_ENVIRONMENT=production
VITE_SENTRY_DSN=your_sentry_dsn
VITE_GOOGLE_ANALYTICS_ID=GA_MEASUREMENT_ID
```

## 🚀 Netlify Deployment

### 1. **Manual Deployment**

```bash
# Install Netlify CLI
npm install -g netlify-cli

# Login to Netlify
netlify login

# Build for production
npm run build:prod

# Deploy to Netlify
netlify deploy --prod --dir=dist
```

### 2. **Automatic Deployment**

1. **Connect Repository**:
   - Go to Netlify Dashboard
   - Click "New site from Git"
   - Connect GitHub repository

2. **Build Settings**:
   ```
   Build command: npm run build
   Publish directory: dist
   Node version: 18.18.0
   ```

3. **Environment Variables**:
   - Go to Site Settings → Environment Variables
   - Add all required variables from `.env.production`

### 3. **Custom Domain Setup**

1. **Add Domain**:
   - Site Settings → Domain Management
   - Add custom domain

2. **DNS Configuration**:
   ```
   Type: CNAME
   Name: www
   Value: your-site.netlify.app
   
   Type: A
   Name: @
   Value: 75.2.60.5
   ```

## ▲ Vercel Deployment

### 1. **Manual Deployment**

```bash
# Install Vercel CLI
npm install -g vercel

# Login to Vercel
vercel login

# Deploy to Vercel
vercel --prod
```

### 2. **Automatic Deployment**

1. **Import Project**:
   - Go to Vercel Dashboard
   - Click "New Project"
   - Import from GitHub

2. **Build Settings**:
   ```
   Framework Preset: Vite
   Build Command: npm run build
   Output Directory: dist
   Node.js Version: 18.x
   ```

3. **Environment Variables**:
   - Project Settings → Environment Variables
   - Add all required variables

## 🤖 CI/CD Pipeline

### GitHub Actions Workflow

The included `.github/workflows/deploy.yml` provides:

1. **Quality Checks**:
   - TypeScript compilation
   - ESLint validation
   - Security audit

2. **Build Process**:
   - Install dependencies
   - Build application
   - Bundle size analysis

3. **Deployment**:
   - Deploy to Netlify
   - Performance testing with Lighthouse

4. **Monitoring**:
   - Deployment notifications
   - Performance reports

### Required GitHub Secrets

```bash
# Netlify
NETLIFY_AUTH_TOKEN=your_netlify_token
NETLIFY_SITE_ID=your_site_id

# Firebase (Production)
VITE_FIREBASE_API_KEY=your_api_key
VITE_FIREBASE_AUTH_DOMAIN=your_auth_domain
VITE_FIREBASE_PROJECT_ID=your_project_id
VITE_FIREBASE_STORAGE_BUCKET=your_storage_bucket
VITE_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
VITE_FIREBASE_APP_ID=your_app_id

# Optional
LHCI_GITHUB_APP_TOKEN=your_lighthouse_token
```

## 🔒 Security Configuration

### 1. **Security Headers**

Configured in `netlify.toml` and `vercel.json`:

```
X-Frame-Options: DENY
X-Content-Type-Options: nosniff
X-XSS-Protection: 1; mode=block
Referrer-Policy: strict-origin-when-cross-origin
Strict-Transport-Security: max-age=31536000
```

### 2. **Firebase Security Rules**

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Require authentication for all operations
    match /{document=**} {
      allow read, write: if request.auth != null;
    }
  }
}
```

### 3. **Environment Security**

- ✅ Use environment variables for all secrets
- ✅ Never commit `.env` files to Git
- ✅ Use different Firebase projects for dev/prod
- ✅ Rotate API keys regularly

## 📊 Performance Optimization

### 1. **Build Optimization**

```bash
# Production build with optimizations
npm run build:prod

# Analyze bundle size
npm run build:analyze
```

### 2. **Caching Strategy**

- **Static Assets**: 1 year cache
- **App Shell**: 1 day cache
- **Service Worker**: No cache
- **API Responses**: 1 hour cache

### 3. **CDN Configuration**

Netlify/Vercel automatically provide:
- Global CDN distribution
- Automatic compression (Gzip/Brotli)
- HTTP/2 support
- Edge caching

## 🔍 Monitoring & Analytics

### 1. **Performance Monitoring**

```bash
# Lighthouse CI for performance testing
npm install -g @lhci/cli

# Run performance audit
lhci autorun
```

### 2. **Error Monitoring**

Optional Sentry integration:

```typescript
// Add to main.tsx
import * as Sentry from "@sentry/react";

if (import.meta.env.VITE_SENTRY_DSN) {
  Sentry.init({
    dsn: import.meta.env.VITE_SENTRY_DSN,
    environment: import.meta.env.VITE_APP_ENVIRONMENT
  });
}
```

### 3. **Analytics**

Optional Google Analytics:

```typescript
// Add to main.tsx
import { gtag } from 'ga-gtag';

if (import.meta.env.VITE_GOOGLE_ANALYTICS_ID) {
  gtag('config', import.meta.env.VITE_GOOGLE_ANALYTICS_ID);
}
```

## 🧪 Testing Strategy

### 1. **Pre-deployment Testing**

```bash
# Type checking
npm run type-check

# Linting
npm run lint

# Build test
npm run build

# Preview test
npm run preview
```

### 2. **Post-deployment Testing**

- ✅ Functionality testing
- ✅ Performance testing (Lighthouse)
- ✅ Security testing
- ✅ Mobile responsiveness
- ✅ PWA functionality

## 🚨 Troubleshooting

### Common Issues

1. **Build Failures**:
   ```bash
   # Clear cache and reinstall
   rm -rf node_modules package-lock.json
   npm install
   npm run build
   ```

2. **Environment Variable Issues**:
   ```bash
   # Validate environment
   node scripts/validate-production-env.js
   ```

3. **Firebase Connection Issues**:
   - Check Firebase project settings
   - Verify API keys are correct
   - Ensure Firestore rules allow access

4. **Performance Issues**:
   ```bash
   # Analyze bundle size
   npm run bundle-analyzer
   
   # Check for unused dependencies
   npx depcheck
   ```

## 📋 Deployment Checklist

### Pre-Deployment
- [ ] Environment variables configured
- [ ] Firebase project setup (production)
- [ ] Security rules updated
- [ ] Build passes locally
- [ ] Performance optimized

### Deployment
- [ ] Repository connected to platform
- [ ] Build settings configured
- [ ] Environment variables set
- [ ] Custom domain configured (if needed)
- [ ] SSL certificate enabled

### Post-Deployment
- [ ] Site loads correctly
- [ ] Authentication works
- [ ] Database operations work
- [ ] PWA features work
- [ ] Performance meets targets
- [ ] Security headers present

## 🎯 Performance Targets

### Lighthouse Scores
- **Performance**: >90
- **Accessibility**: >95
- **Best Practices**: >90
- **SEO**: >90
- **PWA**: >90

### Loading Times
- **First Contentful Paint**: <1.5s
- **Largest Contentful Paint**: <2.5s
- **Time to Interactive**: <3.5s
- **Cumulative Layout Shift**: <0.1

## 🔄 Maintenance

### Regular Tasks
- [ ] Monitor performance metrics
- [ ] Update dependencies monthly
- [ ] Review security alerts
- [ ] Backup database regularly
- [ ] Monitor error rates

### Updates
- [ ] Test in staging first
- [ ] Deploy during low traffic
- [ ] Monitor post-deployment
- [ ] Rollback plan ready

## 🏥 Health Check Script

```bash
# Run production health check
node scripts/health-check.js https://your-domain.com
```

---

🎉 **Deployment Guide Complete!**
📱 OSIS is ready for production deployment with full CI/CD pipeline.
