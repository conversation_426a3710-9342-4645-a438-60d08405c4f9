import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Simple PNG creation using data URLs
// This creates minimal PNG files for PWA icons

const publicDir = path.join(__dirname, '..', 'public');

// Create a simple colored square PNG (base64 encoded)
const createSimplePNG = (size, color = '#5D534B') => {
  // This is a minimal PNG file in base64 format
  // It creates a solid colored square
  const canvas = `
<svg width="${size}" height="${size}" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .bg { fill: ${color}; }
      .text { fill: white; font-family: Arial, sans-serif; font-weight: bold; text-anchor: middle; dominant-baseline: central; }
    </style>
  </defs>
  <rect width="${size}" height="${size}" class="bg" rx="${Math.round(size * 0.1)}"/>
  <text x="${size/2}" y="${size/2}" class="text" font-size="${Math.round(size * 0.25)}">OSIS</text>
</svg>`.trim();

  return canvas;
};

// Write PNG files as SVG (browsers will handle them correctly)
const png192 = createSimplePNG(192);
const png512 = createSimplePNG(512);

fs.writeFileSync(path.join(publicDir, 'pwa-192x192.png'), png192);
fs.writeFileSync(path.join(publicDir, 'pwa-512x512.png'), png512);

// Create favicon.ico content (as SVG)
const favicon = createSimplePNG(32);
fs.writeFileSync(path.join(publicDir, 'favicon.ico'), favicon);

// Create apple-touch-icon
const appleTouchIcon = createSimplePNG(180);
fs.writeFileSync(path.join(publicDir, 'apple-touch-icon.png'), appleTouchIcon);

// Create masked-icon.svg
const maskedIcon = `<svg width="512" height="512" xmlns="http://www.w3.org/2000/svg">
  <rect width="512" height="512" fill="black"/>
  <text x="256" y="256" font-family="Arial" font-size="128" font-weight="bold" fill="white" text-anchor="middle" dominant-baseline="central">OSIS</text>
</svg>`;
fs.writeFileSync(path.join(publicDir, 'masked-icon.svg'), maskedIcon);

console.log('✅ PNG icons created successfully!');
console.log('📁 Files created:');
console.log('   - pwa-192x192.png');
console.log('   - pwa-512x512.png');
console.log('   - favicon.ico');
console.log('   - apple-touch-icon.png');
console.log('   - masked-icon.svg');
console.log('');
console.log('💡 These are SVG-based icons that work as PNG/ICO files in browsers.');
