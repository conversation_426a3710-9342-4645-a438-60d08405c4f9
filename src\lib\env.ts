/**
 * Variabel lingkungan aplikasi
 */

// Environment variables setup with fallbacks untuk production
// Environment variables
const getEnvVar = (key: string, defaultValue: string = ''): string => {
  if (typeof process === 'undefined') {
    return defaultValue;
  }
  return process.env[key] || defaultValue;
};

// Firebase Environment Variables
// Note: Firebase config sudah ada di firebase.ts, ini untuk backup jika diperlukan
export const FIREBASE_API_KEY = getEnvVar('VITE_FIREBASE_API_KEY');
export const FIREBASE_AUTH_DOMAIN = getEnvVar('VITE_FIREBASE_AUTH_DOMAIN');
export const FIREBASE_PROJECT_ID = getEnvVar('VITE_FIREBASE_PROJECT_ID');
export const FIREBASE_STORAGE_BUCKET = getEnvVar('VITE_FIREBASE_STORAGE_BUCKET');
export const FIREBASE_MESSAGING_SENDER_ID = getEnvVar('VITE_FIREBASE_MESSAGING_SENDER_ID');
export const FIREBASE_APP_ID = getEnvVar('VITE_FIREBASE_APP_ID');
export const FIREBASE_MEASUREMENT_ID = getEnvVar('VITE_FIREBASE_MEASUREMENT_ID');

// WhatsApp API Configuration
export const WHATSAPP_API_URL = getEnvVar('VITE_WHATSAPP_API_URL');
export const WHATSAPP_API_KEY = getEnvVar('VITE_WHATSAPP_API_KEY');

