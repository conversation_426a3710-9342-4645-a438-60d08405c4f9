#!/usr/bin/env node

/**
 * Performance monitoring script untuk aplikasi
 * Jalankan dengan: node scripts/performance-monitor.js
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Performance metrics to check
const performanceChecks = [
  {
    name: 'Bundle Size Analysis',
    check: checkBundleSize,
    threshold: 500 // KB
  },
  {
    name: 'Component Complexity',
    check: checkComponentComplexity,
    threshold: 200 // lines per component
  },
  {
    name: 'Import Optimization',
    check: checkImportOptimization,
    threshold: 10 // max imports per file
  },
  {
    name: 'Firebase Query Optimization',
    check: checkFirebaseQueries,
    threshold: 5 // max queries per component
  }
];

function checkBundleSize() {
  const distPath = path.join(__dirname, '../dist');
  
  if (!fs.existsSync(distPath)) {
    return {
      status: 'warning',
      message: 'No build found. Run npm run build first.',
      details: []
    };
  }

  const files = fs.readdirSync(distPath, { recursive: true });
  const jsFiles = files.filter(file => file.endsWith('.js'));
  
  let totalSize = 0;
  const largeFiles = [];

  jsFiles.forEach(file => {
    const filePath = path.join(distPath, file);
    const stats = fs.statSync(filePath);
    const sizeKB = Math.round(stats.size / 1024);
    totalSize += sizeKB;

    if (sizeKB > 100) { // Files larger than 100KB
      largeFiles.push({
        file: file,
        size: `${sizeKB}KB`
      });
    }
  });

  return {
    status: totalSize > 1000 ? 'error' : totalSize > 500 ? 'warning' : 'success',
    message: `Total bundle size: ${totalSize}KB`,
    details: largeFiles.length > 0 ? [
      'Large files detected:',
      ...largeFiles.map(f => `  ${f.file}: ${f.size}`)
    ] : ['All files are optimally sized']
  };
}

function checkComponentComplexity() {
  const srcPath = path.join(__dirname, '../src');
  const componentFiles = [];
  
  function scanDirectory(dir) {
    const items = fs.readdirSync(dir);
    
    items.forEach(item => {
      const itemPath = path.join(dir, item);
      const stat = fs.statSync(itemPath);
      
      if (stat.isDirectory()) {
        scanDirectory(itemPath);
      } else if (item.endsWith('.tsx') || item.endsWith('.ts')) {
        const content = fs.readFileSync(itemPath, 'utf8');
        const lines = content.split('\n').length;
        
        if (lines > 50) { // Only check substantial files
          componentFiles.push({
            file: path.relative(srcPath, itemPath),
            lines: lines,
            complexity: calculateComplexity(content)
          });
        }
      }
    });
  }

  scanDirectory(srcPath);

  const complexFiles = componentFiles.filter(f => f.lines > 200 || f.complexity > 10);
  
  return {
    status: complexFiles.length > 5 ? 'error' : complexFiles.length > 2 ? 'warning' : 'success',
    message: `${complexFiles.length} complex components found`,
    details: complexFiles.length > 0 ? [
      'Complex components (consider refactoring):',
      ...complexFiles.map(f => `  ${f.file}: ${f.lines} lines, complexity: ${f.complexity}`)
    ] : ['All components are reasonably sized']
  };
}

function calculateComplexity(content) {
  // Simple complexity calculation based on:
  // - Number of if statements
  // - Number of loops
  // - Number of functions
  // - Number of useEffect hooks
  
  const ifCount = (content.match(/\bif\s*\(/g) || []).length;
  const loopCount = (content.match(/\b(for|while)\s*\(/g) || []).length;
  const functionCount = (content.match(/\b(function|=>|\bconst\s+\w+\s*=\s*\()/g) || []).length;
  const useEffectCount = (content.match(/useEffect\s*\(/g) || []).length;
  
  return ifCount + loopCount + Math.floor(functionCount / 3) + useEffectCount * 2;
}

function checkImportOptimization() {
  const srcPath = path.join(__dirname, '../src');
  const issues = [];
  
  function scanDirectory(dir) {
    const items = fs.readdirSync(dir);
    
    items.forEach(item => {
      const itemPath = path.join(dir, item);
      const stat = fs.statSync(itemPath);
      
      if (stat.isDirectory()) {
        scanDirectory(itemPath);
      } else if (item.endsWith('.tsx') || item.endsWith('.ts')) {
        const content = fs.readFileSync(itemPath, 'utf8');
        const imports = content.match(/^import\s+.*$/gm) || [];
        
        // Check for potential issues
        const defaultImports = imports.filter(imp => !imp.includes('{'));
        const namedImports = imports.filter(imp => imp.includes('{'));
        
        if (imports.length > 15) {
          issues.push({
            file: path.relative(srcPath, itemPath),
            issue: `Too many imports (${imports.length})`,
            suggestion: 'Consider code splitting or barrel exports'
          });
        }
        
        // Check for unused imports (basic check)
        imports.forEach(imp => {
          const match = imp.match(/import\s+(\w+)/);
          if (match) {
            const importName = match[1];
            const usageCount = (content.match(new RegExp(`\\b${importName}\\b`, 'g')) || []).length;
            if (usageCount <= 1) { // Only imported, not used
              issues.push({
                file: path.relative(srcPath, itemPath),
                issue: `Potentially unused import: ${importName}`,
                suggestion: 'Remove if not needed'
              });
            }
          }
        });
      }
    });
  }

  scanDirectory(srcPath);

  return {
    status: issues.length > 10 ? 'error' : issues.length > 5 ? 'warning' : 'success',
    message: `${issues.length} import optimization opportunities found`,
    details: issues.length > 0 ? [
      'Import optimization suggestions:',
      ...issues.slice(0, 10).map(i => `  ${i.file}: ${i.issue} - ${i.suggestion}`)
    ] : ['Imports are well optimized']
  };
}

function checkFirebaseQueries() {
  const srcPath = path.join(__dirname, '../src');
  const queryIssues = [];
  
  function scanDirectory(dir) {
    const items = fs.readdirSync(dir);
    
    items.forEach(item => {
      const itemPath = path.join(dir, item);
      const stat = fs.statSync(itemPath);
      
      if (stat.isDirectory()) {
        scanDirectory(itemPath);
      } else if (item.endsWith('.tsx') || item.endsWith('.ts')) {
        const content = fs.readFileSync(itemPath, 'utf8');
        
        // Check for Firebase query patterns
        const onSnapshotCount = (content.match(/onSnapshot\s*\(/g) || []).length;
        const getDocsCount = (content.match(/getDocs\s*\(/g) || []).length;
        const collectionCount = (content.match(/collection\s*\(/g) || []).length;
        
        if (onSnapshotCount > 3) {
          queryIssues.push({
            file: path.relative(srcPath, itemPath),
            issue: `Multiple real-time listeners (${onSnapshotCount})`,
            suggestion: 'Consider consolidating listeners or using pagination'
          });
        }
        
        if (getDocsCount > 5) {
          queryIssues.push({
            file: path.relative(srcPath, itemPath),
            issue: `Multiple one-time queries (${getDocsCount})`,
            suggestion: 'Consider caching or batching queries'
          });
        }
        
        // Check for missing query optimization
        if (content.includes('collection(') && !content.includes('limit(')) {
          queryIssues.push({
            file: path.relative(srcPath, itemPath),
            issue: 'Query without limit',
            suggestion: 'Add limit() to prevent large data fetches'
          });
        }
        
        if (content.includes('collection(') && !content.includes('orderBy(')) {
          queryIssues.push({
            file: path.relative(srcPath, itemPath),
            issue: 'Query without ordering',
            suggestion: 'Add orderBy() for consistent results'
          });
        }
      }
    });
  }

  scanDirectory(srcPath);

  return {
    status: queryIssues.length > 5 ? 'error' : queryIssues.length > 2 ? 'warning' : 'success',
    message: `${queryIssues.length} Firebase query optimization opportunities found`,
    details: queryIssues.length > 0 ? [
      'Firebase query optimization suggestions:',
      ...queryIssues.slice(0, 8).map(i => `  ${i.file}: ${i.issue} - ${i.suggestion}`)
    ] : ['Firebase queries are well optimized']
  };
}

function main() {
  console.log('🔍 Running performance analysis...\n');
  
  let totalIssues = 0;
  let criticalIssues = 0;
  
  performanceChecks.forEach(check => {
    console.log(`📊 ${check.name}:`);
    const result = check.check();
    
    // Status indicator
    const statusIcon = {
      success: '✅',
      warning: '⚠️',
      error: '❌'
    }[result.status];
    
    console.log(`   ${statusIcon} ${result.message}`);
    
    if (result.details && result.details.length > 0) {
      result.details.forEach(detail => {
        console.log(`      ${detail}`);
      });
    }
    
    if (result.status === 'warning') totalIssues++;
    if (result.status === 'error') criticalIssues++;
    
    console.log('');
  });
  
  // Summary
  console.log('📋 Performance Analysis Summary:');
  console.log(`   Total checks: ${performanceChecks.length}`);
  console.log(`   Issues found: ${totalIssues}`);
  console.log(`   Critical issues: ${criticalIssues}`);
  
  if (criticalIssues > 0) {
    console.log('\n❌ Critical performance issues detected!');
    console.log('   Please address these issues before deployment.');
    process.exit(1);
  } else if (totalIssues > 0) {
    console.log('\n⚠️  Performance improvements recommended.');
    console.log('   Consider addressing these optimizations.');
  } else {
    console.log('\n✅ Performance analysis passed!');
    console.log('   Application is well optimized.');
  }
}

main();
