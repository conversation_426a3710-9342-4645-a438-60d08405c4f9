import { useState, useEffect } from 'react';
import { Input } from '../components/ui/input';
import { Button } from '../components/ui/button';
import MemberFormModal from '../components/MemberFormModal';
import { memberAPI, Member } from '../services/api';
import { Loader2 } from 'lucide-react';
import { handleError } from '../utils/errorHandler';

interface MemberFormData {
  name: string;
  payment_status: 'paid' | 'unpaid';
  payment_amount: number;
  payment_date: string | null;
}

interface MemberFormProps {
  onSubmit: (member: MemberFormData) => Promise<void>;
  initialData?: Partial<MemberFormData>;
  onClose: () => void;
}

const MemberForm: React.FC<MemberFormProps> = ({ onSubmit, initialData, onClose }) => {
  const [formData, setFormData] = useState<Omit<Member, 'id' | 'created_at'>>({
    name: initialData?.name || '',
    payment_status: initialData?.payment_status || 'unpaid',
    payment_amount: initialData?.payment_amount || 0,
    payment_date: initialData?.payment_date || null
  });

  const [errors, setErrors] = useState<{
    name?: string;
    payment_amount?: string;
  }>({});

  const validateForm = () => {
    const newErrors: typeof errors = {};
    
    if (!formData.name.trim()) {
      newErrors.name = 'Nama harus diisi';
    }
    
    if (formData.payment_amount <= 0) {
      newErrors.payment_amount = 'Jumlah pembayaran harus lebih dari 0';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: name === 'payment_amount' ? (value === '' ? 0 : Number(value)) : value
    }));
    // Clear error when user starts typing
    if (errors[name as keyof typeof errors]) {
      setErrors(prev => ({ ...prev, [name]: undefined }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    try {
      await onSubmit(formData);
      // Reset form setelah submit berhasil
      setFormData({
        name: '',
        payment_status: 'unpaid',
        payment_amount: 0,
        payment_date: null
      });
      setErrors({});
    } catch (error) {
      handleError(error, 'AdminMembersPage');
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <label htmlFor="name" className="block text-sm font-medium text-gray-700">
          Nama
        </label>
        <Input
          id="name"
          name="name"
          value={formData.name}
          onChange={handleChange}
          className={errors.name ? 'border-red-500' : ''}
        />
        {errors.name && <p className="mt-1 text-sm text-red-600">{errors.name}</p>}
      </div>
      
      <div>
        <label htmlFor="payment_amount" className="block text-sm font-medium text-gray-700">
          Jumlah Pembayaran
        </label>
        <Input
          id="payment_amount"
          name="payment_amount"
          type="number"
          value={formData.payment_amount}
          onChange={handleChange}
          className={errors.payment_amount ? 'border-red-500' : ''}
        />
        {errors.payment_amount && <p className="mt-1 text-sm text-red-600">{errors.payment_amount}</p>}
      </div>
      
      <div>
        <label htmlFor="payment_status" className="block text-sm font-medium text-gray-700">
          Status Pembayaran
        </label>
        <select
          id="payment_status"
          name="payment_status"
          value={formData.payment_status}
          onChange={handleChange}
          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
        >
          <option value="unpaid">Belum Lunas</option>
          <option value="paid">Lunas</option>
        </select>
      </div>
      
      {formData.payment_status === 'paid' && (
        <div>
          <label htmlFor="payment_date" className="block text-sm font-medium text-gray-700">
            Tanggal Pembayaran
          </label>
          <Input
            id="payment_date"
            name="payment_date"
            type="date"
            value={formData.payment_date || ''}
            onChange={handleChange}
          />
        </div>
      )}
      
      <div className="flex justify-end space-x-2">
        <Button type="button" variant="outline" onClick={onClose}>
          Batal
        </Button>
        <Button type="submit">
          Simpan
        </Button>
      </div>
    </form>
  );
};

export default function AdminMembersPage() {
  const [members, setMembers] = useState<Member[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [showModal, setShowModal] = useState(false);
  const [selectedMember, setSelectedMember] = useState<Member | undefined>();
  const [toggleLoading, setToggleLoading] = useState<string | null>(null);

  const loadMembers = async () => {
    try {
      setLoading(true);
      const data = await memberAPI.getAll();
      setMembers(data);
    } catch (error) {
      console.error('Error loading members:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadMembers();
  }, []);

  const handleAdd = async (member: Omit<Member, 'id' | 'created_at'>) => {
    try {
      setLoading(true);
      await memberAPI.create(member);
      await loadMembers();
      setShowModal(false);
    } catch (error) {
      console.error('Error adding member:', error);
      alert('Gagal menambah anggota: ' + (error instanceof Error ? error.message : 'Terjadi kesalahan'));
    } finally {
      setLoading(false);
    }
  };

  const handleEditMember = async (member: MemberFormData) => {
    if (!selectedMember?.id) return;
    try {
      setLoading(true);
      await memberAPI.update(selectedMember.id, member);
      await loadMembers();
      setShowModal(false);
      setSelectedMember(undefined);
    } catch (error) {
      console.error('Error editing member:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteMember = async (id: string) => {
    try {
      setLoading(true);
      await memberAPI.delete(id);
      await loadMembers();
    } catch (error) {
      console.error('Error deleting member:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleToggleStatus = async (member: Member) => {
    if (toggleLoading === member.id) return; // Prevent multiple clicks
    
    try {
      setToggleLoading(member.id);
      
      if (member.payment_status === 'unpaid') {
        await memberAPI.markAsPaid(member.id);
      } else {
        await memberAPI.markAsUnpaid(member.id);
      }
      
      // Refresh data
      await loadMembers();
    } catch (error) {
      console.error('Error toggling payment status:', error);
      alert('Gagal mengubah status: ' + (error instanceof Error ? error.message : 'Terjadi kesalahan'));
    } finally {
      setToggleLoading(null);
    }
  };

  const filteredMembers = members.filter(member =>
    member.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <div className="container mx-auto py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Daftar Anggota</h1>
        <Button onClick={() => setShowModal(true)}>Tambah Anggota</Button>
      </div>

      <div className="mb-4">
        <Input
          type="text"
          placeholder="Cari anggota..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="max-w-sm"
        />
      </div>

      {loading ? (
        <div className="flex justify-center items-center h-64">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      ) : (
        <div className="overflow-x-auto">
          <table className="w-full border-collapse">
            <thead>
              <tr className="bg-gray-100">
                <th className="p-4 text-left">Nama</th>
                <th className="p-4 text-left">Status Pembayaran</th>
                <th className="p-4 text-left">Jumlah Pembayaran</th>
                <th className="p-4 text-left">Tanggal Pembayaran</th>
                <th className="p-4 text-left">Aksi</th>
              </tr>
            </thead>
            <tbody>
              {filteredMembers.map((member) => (
                <tr key={member.id} className="border-b">
                  <td className="p-4">{member.name}</td>
                  <td className="p-4">
                    <button
                      onClick={() => handleToggleStatus(member)}
                      disabled={toggleLoading === member.id}
                      className={`px-3 py-1 rounded-full text-sm font-medium cursor-pointer transition-colors duration-200 ${
                        member.payment_status === 'paid' 
                          ? 'bg-green-100 text-green-800 hover:bg-green-200' 
                          : 'bg-red-100 text-red-800 hover:bg-red-200'
                      }`}
                    >
                      {toggleLoading === member.id ? (
                        <Loader2 className="h-4 w-4 animate-spin inline mr-1" />
                      ) : null}
                      {member.payment_status === 'paid' ? 'Lunas' : 'Belum Lunas'}
                    </button>
                  </td>
                  <td className="p-4">Rp {member.payment_amount.toLocaleString('id-ID')}</td>
                  <td className="p-4">
                    {member.payment_date ? new Date(member.payment_date).toLocaleDateString('id-ID') : '-'}
                  </td>
                  <td className="p-4">
                    <div className="flex space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          setSelectedMember(member);
                          setShowModal(true);
                        }}
                      >
                        Edit
                      </Button>
                      <Button
                        variant="destructive"
                        size="sm"
                        onClick={() => handleDeleteMember(member.id)}
                      >
                        Hapus
                      </Button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}

      <MemberFormModal
        isOpen={showModal}
        onClose={() => {
          setShowModal(false);
          setSelectedMember(undefined);
        }}
        onSubmit={selectedMember ? handleEditMember : handleAdd}
        member={selectedMember}
      />
    </div>
  );
} 