const axios = require('axios');

exports.handler = async function(event, context) {
  // URL WhatsApp API di Railway
  const WHATSAPP_API_URL = process.env.WHATSAPP_API_URL || 'https://wabot-production-20ec.up.railway.app';
  
  try {
    // Coba akses root URL untuk melihat struktur API
    const response = await axios.get(WHATSAPP_API_URL);
    
    return {
      statusCode: 200,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        status: true,
        message: 'API info',
        url: WHATSAPP_API_URL,
        responseData: response.data,
      })
    };
  } catch (error) {
    console.error('Error checking API:', error.message);
    
    return {
      statusCode: 500,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        status: false,
        message: `Error: ${error.message}`,
        url: WHATSAPP_API_URL,
        errorDetails: error.response?.data || null
      })
    };
  }
}; 