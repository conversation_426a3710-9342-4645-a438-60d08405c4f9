import { 
  Query, 
  onSnapshot, 
  DocumentData, 
  QuerySnapshot,
  FirestoreError,
  enableNetwork,
  disableNetwork
} from 'firebase/firestore';
import { db } from '../lib/firebase';

// Connection state management
let isOnline = navigator.onLine;
let listeners: Map<string, () => void> = new Map();
let reconnectAttempts = 0;
const MAX_RECONNECT_ATTEMPTS = 5;

// Listen for online/offline events
window.addEventListener('online', () => {
  isOnline = true;
  reconnectAttempts = 0;
  handleReconnection();
});

window.addEventListener('offline', () => {
  isOnline = false;
  handleDisconnection();
});

// Optimized listener manager
export class FirestoreListenerManager {
  private static instance: FirestoreListenerManager;
  private listeners: Map<string, {
    unsubscribe: () => void;
    query: Query;
    callback: (snapshot: QuerySnapshot<DocumentData>) => void;
    errorCallback?: (error: FirestoreError) => void;
    lastUpdate: number;
    retryCount: number;
  }> = new Map();

  private constructor() {}

  static getInstance(): FirestoreListenerManager {
    if (!FirestoreListenerManager.instance) {
      FirestoreListenerManager.instance = new FirestoreListenerManager();
    }
    return FirestoreListenerManager.instance;
  }

  // Add optimized listener with retry logic
  addListener(
    id: string,
    query: Query,
    callback: (snapshot: QuerySnapshot<DocumentData>) => void,
    errorCallback?: (error: FirestoreError) => void
  ): () => void {
    // Remove existing listener if any
    this.removeListener(id);

    const setupListener = () => {
      const unsubscribe = onSnapshot(
        query,
        (snapshot) => {
          this.listeners.set(id, {
            ...this.listeners.get(id)!,
            lastUpdate: Date.now(),
            retryCount: 0
          });
          callback(snapshot);
        },
        (error) => {
          console.error(`Firestore listener error for ${id}:`, error);
          
          const listener = this.listeners.get(id);
          if (listener) {
            listener.retryCount++;
            
            // Retry logic for transient errors
            if (listener.retryCount < 3 && this.isRetryableError(error)) {
              console.log(`Retrying listener ${id} (attempt ${listener.retryCount})`);
              setTimeout(() => {
                this.removeListener(id);
                this.addListener(id, query, callback, errorCallback);
              }, Math.pow(2, listener.retryCount) * 1000); // Exponential backoff
            } else if (errorCallback) {
              errorCallback(error);
            }
          }
        }
      );

      this.listeners.set(id, {
        unsubscribe,
        query,
        callback,
        errorCallback,
        lastUpdate: Date.now(),
        retryCount: 0
      });

      return unsubscribe;
    };

    return setupListener();
  }

  // Remove listener
  removeListener(id: string): void {
    const listener = this.listeners.get(id);
    if (listener) {
      listener.unsubscribe();
      this.listeners.delete(id);
    }
  }

  // Remove all listeners
  removeAllListeners(): void {
    this.listeners.forEach((listener) => {
      listener.unsubscribe();
    });
    this.listeners.clear();
  }

  // Get listener stats
  getStats() {
    const stats = {
      totalListeners: this.listeners.size,
      activeListeners: 0,
      staleListeners: 0,
      erroredListeners: 0
    };

    const now = Date.now();
    this.listeners.forEach((listener) => {
      const timeSinceUpdate = now - listener.lastUpdate;
      
      if (listener.retryCount > 0) {
        stats.erroredListeners++;
      } else if (timeSinceUpdate > 5 * 60 * 1000) { // 5 minutes
        stats.staleListeners++;
      } else {
        stats.activeListeners++;
      }
    });

    return stats;
  }

  // Check if error is retryable
  private isRetryableError(error: FirestoreError): boolean {
    const retryableCodes = [
      'unavailable',
      'deadline-exceeded',
      'resource-exhausted',
      'internal',
      'unknown'
    ];
    
    return retryableCodes.includes(error.code);
  }

  // Reconnect all listeners
  reconnectAll(): void {
    const listenersToReconnect = Array.from(this.listeners.entries());
    
    listenersToReconnect.forEach(([id, listener]) => {
      this.removeListener(id);
      this.addListener(id, listener.query, listener.callback, listener.errorCallback);
    });
  }
}

// Connection management
const handleReconnection = async () => {
  try {
    await enableNetwork(db);
    
    // Reconnect all listeners
    const manager = FirestoreListenerManager.getInstance();
    manager.reconnectAll();
    
  } catch (error) {
    console.error('Failed to enable Firestore network:', error);
    
    // Retry with exponential backoff
    if (reconnectAttempts < MAX_RECONNECT_ATTEMPTS) {
      reconnectAttempts++;
      const delay = Math.pow(2, reconnectAttempts) * 1000;
      setTimeout(handleReconnection, delay);
    }
  }
};

const handleDisconnection = async () => {
  try {
    await disableNetwork(db);
  } catch (error) {
    console.error('Failed to disable Firestore network:', error);
  }
};

// Performance monitoring
export const performanceMonitor = {
  // Track query performance
  trackQuery: (queryName: string, startTime: number) => {
    const duration = Date.now() - startTime;
    
    // Log slow queries
    if (duration > 2000) {
      console.warn(`⚠️ Slow query detected: ${queryName} (${duration}ms)`);
    }
    
    return duration;
  },

  // Track listener performance
  trackListener: (listenerId: string, documentCount: number) => {
    
    // Log large datasets
    if (documentCount > 100) {
      console.warn(`⚠️ Large dataset: ${listenerId} (${documentCount} documents)`);
    }
  },

  // Get performance stats
  getStats: () => {
    const manager = FirestoreListenerManager.getInstance();
    return {
      ...manager.getStats(),
      isOnline,
      reconnectAttempts
    };
  }
};

// Utility functions
export const firestoreUtils = {
  // Get singleton listener manager
  getListenerManager: () => FirestoreListenerManager.getInstance(),

  // Check connection status
  isOnline: () => isOnline,

  // Force reconnection
  forceReconnect: handleReconnection,

  // Get performance stats
  getPerformanceStats: performanceMonitor.getStats,

  // Cleanup all resources
  cleanup: () => {
    const manager = FirestoreListenerManager.getInstance();
    manager.removeAllListeners();
  }
};

// Auto-cleanup on page unload
window.addEventListener('beforeunload', () => {
  firestoreUtils.cleanup();
});

export default FirestoreListenerManager;
