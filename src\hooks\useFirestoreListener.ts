import { useEffect, useRef, useState, useCallback } from 'react';
import { 
  Query, 
  onSnapshot, 
  DocumentData, 
  QuerySnapshot,
  FirestoreError 
} from 'firebase/firestore';

interface UseFirestoreListenerOptions {
  enabled?: boolean;
  onError?: (error: FirestoreError) => void;
  cacheTimeout?: number; // Cache timeout in milliseconds
}

interface CacheEntry<T> {
  data: T[];
  timestamp: number;
  query: string;
}

// Simple in-memory cache
const cache = new Map<string, CacheEntry<DocumentData>>();

// Generate cache key from query
const generateCacheKey = (query: Query): string => {
  // This is a simplified cache key generation
  // In production, you might want a more sophisticated approach
  return query.toString();
};

export const useFirestoreListener = <T extends DocumentData>(
  query: Query | null,
  options: UseFirestoreListenerOptions = {}
) => {
  const {
    enabled = true,
    onError,
    cacheTimeout = 5 * 60 * 1000 // 5 minutes default cache
  } = options;

  const [data, setData] = useState<T[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<FirestoreError | null>(null);
  const unsubscribeRef = useRef<(() => void) | null>(null);
  const isInitialLoad = useRef(true);

  // Clear cache entry
  const clearCache = useCallback((cacheKey: string) => {
    cache.delete(cacheKey);
  }, []);

  // Get cached data if available and not expired
  const getCachedData = useCallback((cacheKey: string): T[] | null => {
    const cached = cache.get(cacheKey);
    if (!cached) return null;

    const isExpired = Date.now() - cached.timestamp > cacheTimeout;
    if (isExpired) {
      cache.delete(cacheKey);
      return null;
    }

    return cached.data;
  }, [cacheTimeout]);

  // Set cache data
  const setCachedData = useCallback((cacheKey: string, data: T[]) => {
    cache.set(cacheKey, {
      data,
      timestamp: Date.now(),
      query: cacheKey
    });
  }, []);

  useEffect(() => {
    if (!query || !enabled) {
      setLoading(false);
      return;
    }

    const cacheKey = generateCacheKey(query);

    // Check cache first
    const cachedData = getCachedData(cacheKey);
    if (cachedData && isInitialLoad.current) {
      setData(cachedData);
      setLoading(false);
      isInitialLoad.current = false;
    }

    // Set up real-time listener
    const unsubscribe = onSnapshot(
      query,
      (snapshot: QuerySnapshot<DocumentData>) => {
        const newData: T[] = [];
        snapshot.forEach((doc) => {
          newData.push({
            id: doc.id,
            ...doc.data()
          } as unknown as T);
        });

        setData(newData);
        setLoading(false);
        setError(null);
        isInitialLoad.current = false;

        // Update cache
        setCachedData(cacheKey, newData);
      },
      (error: FirestoreError) => {
        console.error('Firestore listener error:', error);
        setError(error);
        setLoading(false);

        // Call custom error handler
        if (onError) {
          onError(error);
        }

        // If we have cached data, use it as fallback
        const cachedData = getCachedData(cacheKey);
        if (cachedData) {
          setData(cachedData);
        }
      }
    );

    unsubscribeRef.current = unsubscribe;

    return () => {
      if (unsubscribeRef.current) {
        unsubscribeRef.current();
        unsubscribeRef.current = null;
      }
    };
  }, [query, enabled, onError, getCachedData, setCachedData]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (unsubscribeRef.current) {
        unsubscribeRef.current();
      }
    };
  }, []);

  // Manual refresh function
  const refresh = useCallback(() => {
    if (query) {
      const cacheKey = generateCacheKey(query);
      clearCache(cacheKey);
      setLoading(true);
      isInitialLoad.current = true;
    }
  }, [query, clearCache]);

  return {
    data,
    loading,
    error,
    refresh
  };
};

// Hook for optimized collection listening with pagination
export const useOptimizedCollection = <T extends DocumentData>(
  query: Query | null,
  options: UseFirestoreListenerOptions & {
    pageSize?: number;
    enablePagination?: boolean;
  } = {}
) => {
  const {
    pageSize = 50,
    enablePagination = false,
    ...listenerOptions
  } = options;

  const [currentPage, setCurrentPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);

  const { data, loading, error, refresh } = useFirestoreListener<T>(
    query,
    listenerOptions
  );

  // Paginated data
  const paginatedData = enablePagination 
    ? data.slice(0, currentPage * pageSize)
    : data;

  // Check if there's more data
  useEffect(() => {
    if (enablePagination) {
      setHasMore(data.length > currentPage * pageSize);
    }
  }, [data.length, currentPage, pageSize, enablePagination]);

  const loadMore = useCallback(() => {
    if (hasMore && enablePagination) {
      setCurrentPage(prev => prev + 1);
    }
  }, [hasMore, enablePagination]);

  const resetPagination = useCallback(() => {
    setCurrentPage(1);
  }, []);

  return {
    data: paginatedData,
    allData: data,
    loading,
    error,
    refresh,
    // Pagination
    currentPage,
    hasMore,
    loadMore,
    resetPagination,
    totalItems: data.length
  };
};

// Cache management utilities
export const cacheUtils = {
  // Clear all cache
  clearAll: () => {
    cache.clear();
  },

  // Clear expired cache entries
  clearExpired: (timeout: number = 5 * 60 * 1000) => {
    const now = Date.now();
    for (const [key, entry] of cache.entries()) {
      if (now - entry.timestamp > timeout) {
        cache.delete(key);
      }
    }
  },

  // Get cache size
  getSize: () => cache.size,

  // Get cache info
  getInfo: () => {
    const entries = Array.from(cache.entries()).map(([key, entry]) => ({
      key,
      timestamp: entry.timestamp,
      dataLength: entry.data.length,
      age: Date.now() - entry.timestamp
    }));
    
    return {
      totalEntries: cache.size,
      entries
    };
  }
};
