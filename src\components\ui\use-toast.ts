import { toast as sonnerToast } from 'sonner';

// Simple toast implementation using sonner
export const toast = (options: { title?: string; description?: string; variant?: string }) => {
  if (options.variant === 'destructive') {
    sonnerToast.error(options.title || options.description || 'Error');
  } else {
    sonnerToast.success(options.title || options.description || 'Success');
  }
};

export const useToast = () => {
  return { toast };
};
