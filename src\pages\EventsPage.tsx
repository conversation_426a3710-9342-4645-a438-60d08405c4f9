import { useState } from 'react';
import { Calendar, MessageSquare, DollarSign, Info } from 'lucide-react';
import { motion } from 'framer-motion';
import EventCard from '../components/EventCard';
import Modal from '../components/Modal';
import { formatDate, formatRupiah } from '../utils/formatters';
import { Event } from '../services/api';

// Interface untuk konfigurasi iuran
interface DuesConfiguration {
  pemuda_amount: number;
  pemudi_amount: number;
  pelajar_amount: number;
  notes: string;
}
import Loader from '../components/Loader';
import PageTitle from '../components/PageTitle';
import { useEventsContext } from '../context/EventsContext';
import { useDuesConfigContext } from '../context/DuesConfigContext';

const EventsPage = () => {
  const { events, loading: eventsLoading } = useEventsContext();
  const { config: duesConfig, loading: configLoading } = useDuesConfigContext();
  const [selectedEvent, setSelectedEvent] = useState<any>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  // Data sudah otomatis dimuat dari Context

  const handleOpenModal = (eventId: string) => {
    const event = events.find(e => e.id === eventId);
    if (event) {
      setSelectedEvent(event);
      setIsModalOpen(true);
    }
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedEvent(null);
  };

  if (eventsLoading || configLoading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[70vh] px-4">
        <Loader size="large" variant="default" text="Memuat Data dari Database..." />
      </div>
    );
  }

  if (!events || events.length === 0) {
    return (
      <div className="min-h-screen bg-[#F9F9F9] text-[#5D534B] p-4 sm:p-8">
        <div className="max-w-7xl mx-auto">
          <PageTitle title="Agenda & Kegiatan" borderColor="border-[#FCE09B]" />

          <div className="grid grid-cols-1 gap-6">
            <div className="col-span-full flex flex-col items-center justify-center p-4 sm:p-8 rounded-lg border border-[#9DE0D2] bg-white text-[#5D534B]">
              <Calendar size={64} className="text-[#9DE0D2] mb-4" />
              <p className="text-xl font-medium mb-2 text-center">Belum ada agenda</p>
              <p className="text-sm opacity-70 mb-4 text-center">Belum ada agenda atau kegiatan yang ditambahkan</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-[#F9F9F9] text-[#5D534B] p-4 sm:p-8">
      <div className="max-w-7xl mx-auto">
        <PageTitle title="Agenda & Kegiatan" borderColor="border-[#FCE09B]" />
      
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 mb-12">
          {events.map((event) => (
            <EventCard 
              key={event.id} 
              event={event} 
              onClick={() => handleOpenModal(event.id)} 
            />
          ))}
        </div>

        <div className="mt-10 pt-8 border-t-2 border-[#5D534B]/20">
          <h2 className="text-2xl font-bold text-[#5D534B] mb-6 text-center sm:text-left flex items-center justify-center sm:justify-start">
            Informasi Iuran Anggota
          </h2>

          {duesConfig && (
            <div className="bg-white p-4 sm:p-6 rounded-2xl border-4 border-[#5D534B] shadow-[6px_6px_0px_rgba(93,83,75,0.8)] sm:shadow-[8px_8px_0px_rgba(93,83,75,0.8)] space-y-4">
              <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 text-center">
                <div className="p-3 bg-[#FCE09B]/70 rounded-xl border-2 border-[#5D534B] shadow-[4px_4px_0px_rgba(93,83,75,0.7)]">
                  <p className="text-sm font-medium text-[#5D534B]/90 mb-1">Pemuda</p>
                  <p className="text-xl font-bold text-[#5D534B]">{formatRupiah(duesConfig.pemuda_amount)}</p>
                </div>
                <div className="p-3 bg-[#FF9898]/70 rounded-xl border-2 border-[#5D534B] shadow-[4px_4px_0px_rgba(93,83,75,0.7)]">
                  <p className="text-sm font-medium text-[#5D534B]/90 mb-1">Pemudi</p>
                  <p className="text-xl font-bold text-[#5D534B]">{formatRupiah(duesConfig.pemudi_amount)}</p>
                </div>
                <div className="p-3 bg-[#9DE0D2]/70 rounded-xl border-2 border-[#5D534B] shadow-[4px_4px_0px_rgba(93,83,75,0.7)]">
                  <p className="text-sm font-medium text-[#5D534B]/90 mb-1">Pelajar</p>
                  <p className="text-xl font-bold text-[#5D534B]">{formatRupiah(duesConfig.pelajar_amount)}</p>
                </div>
              </div>
              {duesConfig.notes && (
                <div className="pt-4 border-t border-[#5D534B]/10">
                  <h3 className="text-sm font-semibold text-[#5D534B] mb-2 flex items-center">
                    <Info size={16} className="mr-2 text-[#B39DDB]"/>
                    Catatan:
                  </h3>
                  <p className="text-sm text-[#5D534B]/90 whitespace-pre-line bg-[#F0EFEB] p-3 rounded-md border border-[#5D534B]/10">
                    {duesConfig.notes}
                  </p>
                </div>
              )}
            </div>
          )}
        </div>

        {selectedEvent && (
          <Modal
            isOpen={isModalOpen}
            onClose={handleCloseModal}
            title={selectedEvent.title}
          >
            <div className="space-y-4 text-[#5D534B]">
              <div>
                <h3 className="font-medium mb-1">Deskripsi</h3>
                <p className="text-sm opacity-80 whitespace-pre-line max-h-64 overflow-y-auto pr-2 scrollbar-thin scrollbar-thumb-[#9DE0D2] scrollbar-track-gray-100">
                  {selectedEvent.description || "Tidak ada deskripsi"}
                </p>
              </div>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div>
                  <h3 className="font-medium">Tanggal & Waktu</h3>
                  <p className="text-sm opacity-80 flex items-center">
                    <Calendar className="w-4 h-4 mr-2 text-[#9DE0D2] flex-shrink-0" />
                    <span>{formatDate(selectedEvent.date)} {selectedEvent.time}</span>
                  </p>
                </div>
                <div>
                  <h3 className="font-medium">Lokasi</h3>
                  <p className="text-sm opacity-80 flex items-center">
                    <MessageSquare className="w-4 h-4 mr-2 text-[#FCE09B] flex-shrink-0" />
                    <span>{selectedEvent.location}</span>
                  </p>
                </div>
              </div>
            </div>
          </Modal>
        )}
      </div>
    </div>
  );
};

export default EventsPage;
