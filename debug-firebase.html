<!DOCTYPE html>
<html>
<head>
    <title>Firebase Debug</title>
</head>
<body>
    <h1>Firebase Connection Test</h1>
    <div id="status">Testing...</div>
    <div id="data"></div>

    <script type="module">
        import { initializeApp } from 'https://www.gstatic.com/firebasejs/11.8.1/firebase-app.js';
        import { getFirestore, collection, getDocs } from 'https://www.gstatic.com/firebasejs/11.8.1/firebase-firestore.js';

        // GANTI DENGAN CONFIG FIREBASE YANG BENAR!
        const firebaseConfig = {
            apiKey: "YOUR_API_KEY",
            authDomain: "YOUR_PROJECT.firebaseapp.com",
            projectId: "YOUR_PROJECT_ID",
            storageBucket: "YOUR_PROJECT.firebasestorage.app",
            messagingSenderId: "YOUR_SENDER_ID",
            appId: "YOUR_APP_ID"
        };

        const app = initializeApp(firebaseConfig);
        const db = getFirestore(app);

        async function testConnection() {
            try {
                console.log('Testing Firebase connection...');
                const membersRef = collection(db, 'members');
                const snapshot = await getDocs(membersRef);
                
                document.getElementById('status').innerHTML = `✅ Connected! Found ${snapshot.size} documents`;
                
                let html = '<h3>Members:</h3>';
                snapshot.forEach((doc) => {
                    html += `<p>${doc.id}: ${JSON.stringify(doc.data())}</p>`;
                });
                document.getElementById('data').innerHTML = html;
                
            } catch (error) {
                console.error('Firebase error:', error);
                document.getElementById('status').innerHTML = `❌ Error: ${error.message}`;
            }
        }

        testConnection();
    </script>
</body>
</html>
