#!/usr/bin/env node

/**
 * Production Environment Validation Script
 * Validates all required environment variables for production deployment
 */

import { readFileSync } from 'fs';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Required environment variables for production
const REQUIRED_ENV_VARS = [
  'VITE_FIREBASE_API_KEY',
  'VITE_FIREBASE_AUTH_DOMAIN',
  'VITE_FIREBASE_PROJECT_ID',
  'VITE_FIREBASE_STORAGE_BUCKET',
  'VITE_FIREBASE_MESSAGING_SENDER_ID',
  'VITE_FIREBASE_APP_ID'
];

// Optional but recommended environment variables
const RECOMMENDED_ENV_VARS = [
  'VITE_APP_NAME',
  'VITE_APP_VERSION',
  'VITE_APP_ENVIRONMENT',
  'VITE_SENTRY_DSN',
  'VITE_GOOGLE_ANALYTICS_ID'
];

// Security validation patterns
const SECURITY_PATTERNS = {
  VITE_FIREBASE_API_KEY: /^[A-Za-z0-9_-]{39}$/,
  VITE_FIREBASE_PROJECT_ID: /^[a-z0-9-]+$/,
  VITE_FIREBASE_AUTH_DOMAIN: /^[a-z0-9-]+\.firebaseapp\.com$/,
  VITE_FIREBASE_STORAGE_BUCKET: /^[a-z0-9-]+\.appspot\.com$/,
  VITE_FIREBASE_MESSAGING_SENDER_ID: /^\d{12}$/,
  VITE_FIREBASE_APP_ID: /^1:\d{12}:web:[a-f0-9]{40}$/
};

function validateEnvironment() {
  console.log('🔍 Validating Production Environment Variables...\n');

  let hasErrors = false;
  let hasWarnings = false;

  // Check required variables
  console.log('📋 Required Variables:');
  for (const envVar of REQUIRED_ENV_VARS) {
    const value = process.env[envVar];
    
    if (!value) {
      console.log(`❌ ${envVar}: Missing (REQUIRED)`);
      hasErrors = true;
    } else if (value.includes('your_') || value.includes('_here')) {
      console.log(`⚠️  ${envVar}: Contains placeholder value`);
      hasWarnings = true;
    } else if (SECURITY_PATTERNS[envVar] && !SECURITY_PATTERNS[envVar].test(value)) {
      console.log(`⚠️  ${envVar}: Invalid format`);
      hasWarnings = true;
    } else {
      const maskedValue = value.length > 10 ? 
        value.substring(0, 6) + '...' + value.substring(value.length - 4) : 
        '***';
      console.log(`✅ ${envVar}: ${maskedValue}`);
    }
  }

  console.log('\n📋 Recommended Variables:');
  for (const envVar of RECOMMENDED_ENV_VARS) {
    const value = process.env[envVar];
    
    if (!value) {
      console.log(`⚠️  ${envVar}: Not set (recommended)`);
      hasWarnings = true;
    } else {
      console.log(`✅ ${envVar}: ${value}`);
    }
  }

  // Security checks
  console.log('\n🔒 Security Checks:');
  
  // Check for development values in production
  const devIndicators = ['localhost', 'dev', 'test', 'demo'];
  let hasDevValues = false;
  
  for (const envVar of REQUIRED_ENV_VARS) {
    const value = process.env[envVar];
    if (value) {
      for (const indicator of devIndicators) {
        if (value.toLowerCase().includes(indicator)) {
          console.log(`⚠️  ${envVar}: Contains development indicator '${indicator}'`);
          hasWarnings = true;
          hasDevValues = true;
        }
      }
    }
  }
  
  if (!hasDevValues) {
    console.log('✅ No development values detected');
  }

  // Check environment mode
  const nodeEnv = process.env.NODE_ENV;
  const viteEnv = process.env.VITE_APP_ENVIRONMENT;
  
  console.log(`📊 NODE_ENV: ${nodeEnv || 'not set'}`);
  console.log(`📊 VITE_APP_ENVIRONMENT: ${viteEnv || 'not set'}`);
  
  if (nodeEnv !== 'production' && viteEnv !== 'production') {
    console.log('⚠️  Neither NODE_ENV nor VITE_APP_ENVIRONMENT is set to "production"');
    hasWarnings = true;
  }

  // Firebase project validation
  console.log('\n🔥 Firebase Configuration:');
  const projectId = process.env.VITE_FIREBASE_PROJECT_ID;
  const authDomain = process.env.VITE_FIREBASE_AUTH_DOMAIN;
  const storageBucket = process.env.VITE_FIREBASE_STORAGE_BUCKET;
  
  if (projectId && authDomain && storageBucket) {
    const expectedAuthDomain = `${projectId}.firebaseapp.com`;
    const expectedStorageBucket = `${projectId}.appspot.com`;
    
    if (authDomain === expectedAuthDomain) {
      console.log('✅ Auth domain matches project ID');
    } else {
      console.log(`⚠️  Auth domain mismatch. Expected: ${expectedAuthDomain}, Got: ${authDomain}`);
      hasWarnings = true;
    }
    
    if (storageBucket === expectedStorageBucket) {
      console.log('✅ Storage bucket matches project ID');
    } else {
      console.log(`⚠️  Storage bucket mismatch. Expected: ${expectedStorageBucket}, Got: ${storageBucket}`);
      hasWarnings = true;
    }
  }

  // Summary
  console.log('\n📊 Validation Summary:');
  
  if (hasErrors) {
    console.log('❌ Validation FAILED - Missing required environment variables');
    console.log('\n💡 To fix:');
    console.log('1. Copy .env.production to your deployment platform');
    console.log('2. Replace placeholder values with actual Firebase credentials');
    console.log('3. Set environment variables in your deployment platform');
    process.exit(1);
  } else if (hasWarnings) {
    console.log('⚠️  Validation PASSED with warnings');
    console.log('\n💡 Recommendations:');
    console.log('1. Set all recommended environment variables');
    console.log('2. Verify Firebase configuration values');
    console.log('3. Ensure production values are used');
    process.exit(0);
  } else {
    console.log('✅ Validation PASSED - All environment variables are properly configured');
    process.exit(0);
  }
}

// Check if running in CI/CD environment
function checkCIEnvironment() {
  const ciIndicators = ['CI', 'GITHUB_ACTIONS', 'NETLIFY', 'VERCEL'];
  const isCI = ciIndicators.some(indicator => process.env[indicator]);
  
  if (isCI) {
    console.log('🤖 Running in CI/CD environment');
    
    // Additional CI-specific checks
    if (process.env.GITHUB_ACTIONS) {
      console.log('📦 GitHub Actions detected');
    }
    if (process.env.NETLIFY) {
      console.log('🌐 Netlify deployment detected');
    }
    if (process.env.VERCEL) {
      console.log('▲ Vercel deployment detected');
    }
  }
  
  return isCI;
}

// Main execution
function main() {
  console.log('🚀 OSIS Production Environment Validation\n');
  
  const isCI = checkCIEnvironment();
  
  if (isCI) {
    console.log('');
  }
  
  validateEnvironment();
}

// Run validation
main();
