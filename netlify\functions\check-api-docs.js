const axios = require('axios');

/**
 * Fungsi untuk memeriksa dokumentasi API WhatsApp
 */
exports.handler = async function(event, context) {
  const WHATSAPP_API_URL = process.env.WHATSAPP_API_URL || 'https://wabot-production-20ec.up.railway.app';
  
  try {
    // Coba akses halaman dokumentasi API
    const docUrl = `${WHATSAPP_API_URL}/utils/api-list`;
    console.log(`Memeriksa dokumentasi di: ${docUrl}`);
    
    const response = await axios.get(docUrl);
    
    return {
      statusCode: 200,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        status: true,
        message: 'API documentation info',
        url: docUrl,
        responseData: response.data,
      })
    };
  } catch (error) {
    console.error('Error checking API documentation:', error.message);
    
    return {
      statusCode: 500,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        status: false,
        message: `Error: ${error.message}`,
        error: error.response?.data || null
      })
    };
  }
}; 