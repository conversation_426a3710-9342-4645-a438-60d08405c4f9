import { create } from 'zustand';
import { Event, eventAPI } from '../services/api';

interface EventStore {
  events: Event[];
  isLoading: boolean;
  error: string | null;
  fetchEvents: () => Promise<void>;
  addEvent: (event: Omit<Event, 'id' | 'created_at'>) => Promise<void>;
  updateEvent: (id: string, event: Partial<Event>) => Promise<void>;
  deleteEvent: (id: string) => Promise<void>;
}

const useEventsStore = create<EventStore>((set) => ({
  events: [],
  isLoading: false,
  error: null,

  fetchEvents: async () => {
    set({ isLoading: true, error: null });
    try {
      const events = await eventAPI.getAll();
      set({ events, isLoading: false });
    } catch (error) {
      set({ error: (error as Error).message, isLoading: false });
    }
  },

  addEvent: async (event: Omit<Event, 'id' | 'created_at'>) => {
    set({ isLoading: true, error: null });
    try {
      const newEvent = await eventAPI.create(event);
      set((state: EventStore) => ({ 
        events: [...state.events, newEvent],
        isLoading: false
      }));
    } catch (error) {
      set({ error: (error as Error).message, isLoading: false });
    }
  },

  updateEvent: async (id: string, event: Partial<Event>) => {
    set({ isLoading: true, error: null });
    try {
      const updatedEvent = await eventAPI.update(id, event);
      set((state: EventStore) => ({
        events: state.events.map((e: Event) => e.id === id ? updatedEvent : e),
        isLoading: false
      }));
    } catch (error) {
      set({ error: (error as Error).message, isLoading: false });
    }
  },

  deleteEvent: async (id: string) => {
    set({ isLoading: true, error: null });
    try {
      await eventAPI.delete(id);
      set((state: EventStore) => ({
        events: state.events.filter((e: Event) => e.id !== id),
        isLoading: false
      }));
    } catch (error) {
      set({ error: (error as Error).message, isLoading: false });
    }
  }
}));

export default useEventsStore; 