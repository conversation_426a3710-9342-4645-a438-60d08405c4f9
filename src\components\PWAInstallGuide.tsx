import React, { useState, useEffect } from 'react';
import { Smartphone, Monitor, Download, Share, Plus, MoreVertical } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';

interface InstallInstructions {
  platform: string;
  icon: React.ElementType;
  steps: string[];
  color: string;
}

const PWAInstallGuide: React.FC = () => {
  const [showGuide, setShowGuide] = useState(false);
  const [currentPlatform, setCurrentPlatform] = useState<InstallInstructions | null>(null);

  const installInstructions: InstallInstructions[] = [
    {
      platform: 'Android Chrome',
      icon: Smartphone,
      color: 'bg-green-500',
      steps: [
        'Buka OSIS di Chrome browser',
        'Tap menu (⋮) di pojok kanan atas',
        'Pilih "Add to Home screen"',
        'Tap "Add" untuk konfirmasi',
        'Icon OSIS akan muncul di home screen'
      ]
    },
    {
      platform: 'iPhone Safari',
      icon: Smartphone,
      color: 'bg-blue-500',
      steps: [
        'Buka OSIS di Safari browser',
        'Tap tombol Share (⬆️) di bawah',
        '<PERSON><PERSON> dan pilih "Add to Home Screen"',
        'Edit nama jika perlu, tap "Add"',
        'Icon OSIS akan muncul di home screen'
      ]
    },
    {
      platform: 'Desktop Chrome',
      icon: Monitor,
      color: 'bg-purple-500',
      steps: [
        'Buka OSIS di Chrome browser',
        'Klik icon install (⬇️) di address bar',
        'Atau klik menu (⋮) > "Install OSIS"',
        'Klik "Install" untuk konfirmasi',
        'OSIS akan terbuka sebagai aplikasi desktop'
      ]
    },
    {
      platform: 'Desktop Edge',
      icon: Monitor,
      color: 'bg-blue-600',
      steps: [
        'Buka OSIS di Microsoft Edge',
        'Klik icon install (⬇️) di address bar',
        'Atau klik menu (⋯) > "Apps" > "Install OSIS"',
        'Klik "Install" untuk konfirmasi',
        'OSIS akan terbuka sebagai aplikasi desktop'
      ]
    }
  ];

  useEffect(() => {
    // Auto-detect platform
    const userAgent = navigator.userAgent.toLowerCase();
    const isIOS = /iphone|ipad|ipod/.test(userAgent);
    const isAndroid = /android/.test(userAgent);
    const isChrome = /chrome/.test(userAgent) && !/edge/.test(userAgent);
    const isEdge = /edge/.test(userAgent);
    const isMobile = /mobile/.test(userAgent);

    if (isIOS) {
      setCurrentPlatform(installInstructions[1]); // iPhone Safari
    } else if (isAndroid && isChrome) {
      setCurrentPlatform(installInstructions[0]); // Android Chrome
    } else if (!isMobile && isEdge) {
      setCurrentPlatform(installInstructions[3]); // Desktop Edge
    } else if (!isMobile && isChrome) {
      setCurrentPlatform(installInstructions[2]); // Desktop Chrome
    } else {
      setCurrentPlatform(installInstructions[0]); // Default to Android
    }
  }, []);

  const handleShowGuide = () => {
    setShowGuide(true);
  };

  const handleCloseGuide = () => {
    setShowGuide(false);
  };

  return (
    <>
      {/* Install Guide Trigger Button */}
      <button
        type="button"
        onClick={handleShowGuide}
        className="fixed bottom-20 right-4 bg-[#5D534B] text-white p-3 rounded-full shadow-lg hover:bg-[#4A453E] transition-colors z-40"
        title="Install Guide"
      >
        <Download size={20} />
      </button>

      {/* Install Guide Modal */}
      <AnimatePresence>
        {showGuide && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50"
            onClick={handleCloseGuide}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-white rounded-lg shadow-xl max-w-md w-full max-h-[80vh] overflow-y-auto"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-xl font-bold text-gray-900">Install OSIS App</h2>
                  <button
                    type="button"
                    onClick={handleCloseGuide}
                    className="text-gray-400 hover:text-gray-600 transition-colors"
                  >
                    ✕
                  </button>
                </div>

                <div className="mb-6">
                  <p className="text-gray-600 text-sm mb-4">
                    Install OSIS sebagai aplikasi di device Anda untuk pengalaman yang lebih baik:
                  </p>
                  
                  <div className="grid grid-cols-2 gap-2 mb-4">
                    <div className="flex items-center space-x-2 text-sm text-gray-600">
                      <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                      <span>Akses cepat</span>
                    </div>
                    <div className="flex items-center space-x-2 text-sm text-gray-600">
                      <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                      <span>Offline mode</span>
                    </div>
                    <div className="flex items-center space-x-2 text-sm text-gray-600">
                      <span className="w-2 h-2 bg-purple-500 rounded-full"></span>
                      <span>Native feel</span>
                    </div>
                    <div className="flex items-center space-x-2 text-sm text-gray-600">
                      <span className="w-2 h-2 bg-orange-500 rounded-full"></span>
                      <span>Auto update</span>
                    </div>
                  </div>
                </div>

                {/* Platform Tabs */}
                <div className="mb-4">
                  <div className="flex space-x-1 bg-gray-100 rounded-lg p-1">
                    {installInstructions.map((platform, index) => (
                      <button
                        key={platform.platform}
                        type="button"
                        onClick={() => setCurrentPlatform(platform)}
                        className={`flex-1 py-2 px-3 rounded-md text-xs font-medium transition-colors ${
                          currentPlatform?.platform === platform.platform
                            ? 'bg-white text-gray-900 shadow-sm'
                            : 'text-gray-600 hover:text-gray-900'
                        }`}
                      >
                        <platform.icon size={16} className="mx-auto mb-1" />
                        <div>{platform.platform}</div>
                      </button>
                    ))}
                  </div>
                </div>

                {/* Install Steps */}
                {currentPlatform && (
                  <div className="space-y-3">
                    <div className={`flex items-center space-x-2 p-3 ${currentPlatform.color} text-white rounded-lg`}>
                      <currentPlatform.icon size={20} />
                      <span className="font-medium">{currentPlatform.platform}</span>
                    </div>
                    
                    <div className="space-y-2">
                      {currentPlatform.steps.map((step, index) => (
                        <div key={index} className="flex items-start space-x-3">
                          <div className="flex-shrink-0 w-6 h-6 bg-gray-200 rounded-full flex items-center justify-center text-xs font-medium text-gray-600">
                            {index + 1}
                          </div>
                          <p className="text-sm text-gray-700">{step}</p>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                <div className="mt-6 pt-4 border-t border-gray-200">
                  <p className="text-xs text-gray-500 text-center">
                    Setelah install, OSIS akan bekerja seperti aplikasi native di device Anda
                  </p>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
};

export default PWAInstallGuide;
