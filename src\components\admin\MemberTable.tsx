import { Table, TableHeader, TableRow, TableHead, TableBody, TableCell } from '@/components/ui/table';
import { Input } from '@/components/ui/input';
import { Edit, Save, Trash } from 'lucide-react';
import { Loader2 } from 'lucide-react';
import { formatRupiah } from '../../utils/formatters';

interface Member {
  id: string;
  name: string;
  payment_status: 'paid' | 'unpaid';
  payment_amount: number;
  payment_date: string | null;
}

interface EditValues {
  name?: string;
  payment_status?: 'paid' | 'unpaid';
  payment_amount?: number;
  payment_date?: string;
}

interface MemberTableProps {
  members: Member[];
  editingId: string | null;
  editValues: EditValues;
  toggleLoading: string | null;
  actionLoading: boolean;
  onEdit: (member: Member) => void;
  onDelete: (id: string) => void;
  onToggleStatus: (member: Member) => void;
  onSaveEdit: () => void;
  onCancelEdit: () => void;
  onChange: (id: string, field: string, value: string | number) => void;
}

const MemberTable: React.FC<MemberTableProps> = ({
  members,
  editingId,
  editValues,
  toggleLoading,
  actionLoading,
  onEdit,
  onDelete,
  onToggleStatus,
  onSaveEdit,
  onCancelEdit,
  onChange
}) => {
  return (
    <div className="hidden md:block overflow-x-auto">
      <Table className="neo-table min-w-full">
        <TableHeader>
          <TableRow>
            <TableHead className="text-sm whitespace-nowrap">Nama</TableHead>
            <TableHead className="text-sm whitespace-nowrap">Status</TableHead>
            <TableHead className="text-sm whitespace-nowrap">Jumlah</TableHead>
            <TableHead className="text-sm whitespace-nowrap">Tanggal</TableHead>
            <TableHead className="text-sm text-right whitespace-nowrap">Aksi</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {members.map((member) => (
            <TableRow key={member.id}>
              <TableCell className="text-sm">
                {editingId === member.id ? (
                  <Input
                    value={editValues.name || ''}
                    onChange={(e) => onChange(member.id, 'name', e.target.value)}
                    className="neo-input text-sm"
                    placeholder="Nama anggota"
                  />
                ) : (
                  <span className="font-medium">{member.name}</span>
                )}
              </TableCell>
              
              <TableCell className="text-sm">
                {editingId === member.id ? (
                  <select
                    id={`table-status-${member.id}`}
                    value={editValues.payment_status || 'unpaid'}
                    onChange={(e) => onChange(member.id, 'payment_status', e.target.value as 'paid' | 'unpaid')}
                    className="neo-input text-sm"
                    aria-label="Status pembayaran anggota"
                    title="Pilih status pembayaran"
                  >
                    <option value="paid">Lunas</option>
                    <option value="unpaid">Belum Lunas</option>
                  </select>
                ) : (
                  <button
                    type="button"
                    onClick={() => onToggleStatus(member)}
                    disabled={toggleLoading === member.id}
                    className={`px-2.5 py-1 rounded-full text-xs sm:text-sm font-medium cursor-pointer transition-all duration-150 min-w-[70px] text-center inline-flex items-center justify-center border-2 border-[#5D534B] bg-white shadow-[3px_3px_0px_rgba(93,83,75,0.7)] active:shadow-[1px_1px_0px_rgba(93,83,75,0.7)] active:translate-x-[2px] active:translate-y-[2px] ${
                      member.payment_status === 'paid'
                        ? 'text-green-600 hover:bg-green-50'
                        : 'text-red-600 hover:bg-red-50'
                    } ${toggleLoading === member.id ? 'opacity-50 cursor-not-allowed' : ''}`}
                  >
                    {toggleLoading === member.id && (
                      <Loader2 className="h-4 w-4 animate-spin inline mr-1" />
                    )}
                    {member.payment_status === 'paid' ? 'Lunas' : 'Belum'}
                  </button>
                )}
              </TableCell>
              
              <TableCell className="text-sm">
                {editingId === member.id ? (
                  <Input
                    type="number"
                    value={editValues.payment_amount || 0}
                    onChange={(e) => onChange(member.id, 'payment_amount', Number(e.target.value))}
                    className="neo-input text-sm"
                    placeholder="Jumlah"
                  />
                ) : (
                  <span className="font-semibold">{formatRupiah(member.payment_amount)}</span>
                )}
              </TableCell>
              
              <TableCell className="text-sm">
                {editingId === member.id ? (
                  <Input
                    type="date"
                    value={editValues.payment_date || ''}
                    onChange={(e) => onChange(member.id, 'payment_date', e.target.value)}
                    className="neo-input text-sm"
                  />
                ) : (
                  <span className="text-gray-600">{member.payment_date || '-'}</span>
                )}
              </TableCell>
              
              <TableCell className="text-right">
                {editingId === member.id ? (
                  <div className="flex justify-end space-x-2">
                    <button
                      type="button"
                      onClick={onSaveEdit}
                      disabled={actionLoading}
                      className="p-2 bg-[#9DE0D2] border-2 border-[#5D534B] rounded-lg hover:bg-[#8DD4C6] transition-colors disabled:opacity-50"
                      title="Simpan perubahan"
                    >
                      {actionLoading ? (
                        <Loader2 className="h-4 w-4 animate-spin" />
                      ) : (
                        <Save size={16} />
                      )}
                    </button>
                    <button
                      type="button"
                      onClick={onCancelEdit}
                      disabled={actionLoading}
                      className="p-2 bg-[#FF9898] border-2 border-[#5D534B] rounded-lg hover:bg-[#FF8A8A] transition-colors disabled:opacity-50"
                      title="Batal edit"
                    >
                      ❌
                    </button>
                  </div>
                ) : (
                  <div className="flex justify-end space-x-2">
                    <button
                      type="button"
                      onClick={() => onEdit(member)}
                      className="p-2 bg-[#9DE0D2] border-2 border-[#5D534B] rounded-lg hover:bg-[#8DD4C6] transition-colors"
                      title="Edit anggota"
                    >
                      <Edit size={16} />
                    </button>
                    <button
                      type="button"
                      onClick={() => onDelete(member.id)}
                      className="p-2 bg-[#FF9898] border-2 border-[#5D534B] rounded-lg hover:bg-[#FF8A8A] transition-colors"
                      title="Hapus anggota"
                    >
                      <Trash size={16} />
                    </button>
                  </div>
                )}
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
};

export default MemberTable;
