// De<PERSON><PERSON>i tipe untuk window global

interface Window {
  Buffer: typeof Buffer;
  process: any;
  TextEncoder: typeof TextEncoder;
  TextDecoder: typeof TextDecoder;
  global: Window;
}

declare module 'jsonwebtoken' {
  export function sign(payload: any, secretOrPrivateKey: string, options?: any): string;
  export function verify(token: string, secretOrPublicKey: string, options?: any): any;
  export function decode(token: string, options?: any): any;
}

declare module 'jws' {
  export function sign(options: any): string;
  export function verify(signature: string, algorithm: string, secretOrKey: string): boolean;
}

declare module 'base64url' {
  export function encode(input: string | Buffer): string;
  export function decode(input: string): string;
}

declare module 'safe-buffer' {
  export class Buffer {
    static from(data: any, encoding?: string): Buffer;
    static alloc(size: number, fill?: any, encoding?: string): Buffer;
    static allocUnsafe(size: number): Buffer;
    static isBuffer(obj: any): boolean;
    static byteLength(string: string, encoding?: string): number;
    static concat(list: Buffer[], totalLength?: number): Buffer;
  }
}

declare module 'util' {
  export function debuglog(section: string): (msg: string, ...args: any[]) => void;
  export function inspect(object: any, options?: any): string;
  export function format(format: any, ...param: any[]): string;
  export function isArray(object: any): boolean;
  export function isRegExp(object: any): boolean;
  export function isDate(object: any): boolean;
  export function isError(object: any): boolean;
  export function inherits(constructor: any, superConstructor: any): void;
  export class TextEncoder {
    encode(input?: string): Uint8Array;
  }
  export class TextDecoder {
    decode(input?: Uint8Array): string;
  }
}

declare module 'stream-browserify' {
  export class Stream {
    pipe<T extends NodeJS.WritableStream>(destination: T, options?: { end?: boolean }): T;
  }
  export class Readable extends Stream {
    readable: boolean;
    read(size?: number): any;
    setEncoding(encoding: string): this;
    pause(): this;
    resume(): this;
    isPaused(): boolean;
    unpipe<T extends NodeJS.WritableStream>(destination?: T): this;
    unshift(chunk: any): void;
    wrap(oldStream: NodeJS.ReadableStream): this;
  }
  export class Writable extends Stream {
    writable: boolean;
    write(chunk: any, encoding?: string, callback?: (error?: Error | null) => void): boolean;
    end(cb?: () => void): void;
    end(chunk: any, cb?: () => void): void;
    end(chunk: any, encoding?: string, cb?: () => void): void;
  }
  export class Duplex extends Readable implements Writable {
    writable: boolean;
    write(chunk: any, encoding?: string, callback?: (error?: Error | null) => void): boolean;
    end(cb?: () => void): void;
    end(chunk: any, cb?: () => void): void;
    end(chunk: any, encoding?: string, cb?: () => void): void;
  }
  export class Transform extends Duplex {
    _transform(chunk: any, encoding: string, callback: (error?: Error, data?: any) => void): void;
    _flush(callback: (error?: Error, data?: any) => void): void;
  }
  export class PassThrough extends Transform {}
}
