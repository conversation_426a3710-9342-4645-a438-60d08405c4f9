// <PERSON><PERSON><PERSON><PERSON> tipe untuk window global

interface Window {
  Buffer: typeof Buffer;
  process: NodeJS.Process;
  TextEncoder: typeof TextEncoder;
  TextDecoder: typeof TextDecoder;
  global: Window;
}

// Type-safe module declarations
declare module 'jsonwebtoken' {
  interface SignOptions {
    algorithm?: string;
    expiresIn?: string | number;
    notBefore?: string | number;
    audience?: string | string[];
    subject?: string;
    issuer?: string;
    jwtid?: string;
    keyid?: string;
    header?: Record<string, unknown>;
    encoding?: string;
  }

  interface VerifyOptions {
    algorithms?: string[];
    audience?: string | RegExp | (string | RegExp)[];
    clockTolerance?: number;
    issuer?: string | string[];
    ignoreExpiration?: boolean;
    ignoreNotBefore?: boolean;
    subject?: string | string[];
    clockTimestamp?: number;
    nonce?: string;
    maxAge?: string | number;
  }

  export function sign(payload: string | Buffer | object, secretOrPrivateKey: string, options?: SignOptions): string;
  export function verify(token: string, secretOrPublicKey: string, options?: VerifyOptions): string | object;
  export function decode(token: string, options?: { complete?: boolean; json?: boolean }): null | { [key: string]: unknown } | string;
}

// Simplified module declarations for browser compatibility
declare module 'jws' {
  interface SignOptions {
    header: Record<string, unknown>;
    payload: string;
    secret: string;
  }

  export function sign(options: SignOptions): string;
  export function verify(signature: string, algorithm: string, secretOrKey: string): boolean;
}

declare module 'base64url' {
  export function encode(input: string | Buffer): string;
  export function decode(input: string): string;
}
