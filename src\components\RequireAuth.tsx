import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
// Asumsi kita punya hook useAuth() atau context untuk cek status login
import { useAuth } from '../context/AuthContext'; // GANTI path jika lokasi AuthContext berbeda
import Loader from './Loader'; // Asumsi ada komponen Loader

interface RequireAuthProps {
  children: React.ReactNode;
}

const RequireAuth: React.FC<RequireAuthProps> = ({ children }) => {
  const { isAuthenticated, isLoading } = useAuth(); // Ambil status dari context/hook
  const location = useLocation();

  if (isLoading) {
    // Tampilkan loading jika status autentikasi sedang diperiksa
    return (
      <div className="min-h-screen w-full flex items-center justify-center bg-[#F9F9F9]">
        <Loader text="Memeriksa autentikasi..." />
      </div>
    );
  }

  if (!isAuthenticated) {
    // Jika tidak terautentikasi, arahkan ke halaman login
    // <PERSON>rim lokasi saat ini agar bisa kembali setelah login berhasil
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  // Jika terautentikasi, tampilkan komponen anak (halaman yang diminta)
  return <>{children}</>;
};

export default RequireAuth; 