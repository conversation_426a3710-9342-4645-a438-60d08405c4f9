# Konfigurasi cloudflare.toml untuk Cloudflare Pages

# Konfigurasi build
[build]
  command = "npm run build"
  publish = "dist"

# Konfigurasi routing
# API routes will be handled by external services

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

# Headers untuk keamanan
[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"

# Pengaturan khusus untuk environment Cloudflare
[site]
  bucket = "./dist"
  entry-point = "dist"

# Tambahkan environment variables di sini untuk produksi
[env.production]
  NODE_VERSION = "18"

# Tambahkan environment variables di sini untuk staging
[env.staging]
  NODE_VERSION = "18"

# Tambahkan environment variables di sini untuk development
[env.development]
  NODE_VERSION = "18"

# Konfigurasi untuk Functions
# Functions are handled by external services (Netlify/Supabase)