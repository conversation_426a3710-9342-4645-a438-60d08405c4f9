import React from 'react';
import { motion } from 'framer-motion';

interface PageTitleProps {
  title: string;
  borderColor?: string;
}

const PageTitle: React.FC<PageTitleProps> = ({ 
  title, 
  borderColor = 'border-[#9DE0D2]' 
}) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="mb-8"
    >
      <h1 
        className={`text-3xl font-bold text-[#5D534B] inline-block pb-2 border-b-4 ${borderColor}`}
      >
        {title}
      </h1>
    </motion.div>
  );
};

export default PageTitle; 