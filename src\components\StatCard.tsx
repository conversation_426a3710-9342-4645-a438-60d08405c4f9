import React from 'react';
import { LucideIcon } from 'lucide-react';

interface StatCardProps {
  title: string;
  value: string | number;
  icon: LucideIcon;
  gradientClass?: string;
  borderColor?: string;
}

const StatCard: React.FC<StatCardProps> = ({ 
  title, 
  value, 
  icon: Icon,
  gradientClass = "neo-gradient-pink",
  borderColor = "border-neo-pink"
}) => {
  return (
    <div className={`neo-card p-4 ${gradientClass} animate-fade-in ${borderColor} transition-all duration-200`}>
      <div className="flex justify-between items-start">
        <div>
          <h3 className="text-sm font-bold mb-1 text-[#5D534B]">{title}</h3>
          <p className="text-xl md:text-2xl font-bold text-[#5D534B]">{value}</p>
        </div>
        <div className="bg-white p-2 border-4 border-[#5D534B] rounded-full shadow-pastel-sm">
          <Icon size={20} strokeWidth={2.5} className="text-[#5D534B]" />
        </div>
      </div>
    </div>
  );
};

export default StatCard;
