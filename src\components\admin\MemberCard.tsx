import { Loader2 } from 'lucide-react';
import { formatRupiah } from '../../utils/formatters';

interface Member {
  id: string;
  name: string;
  payment_status: 'paid' | 'unpaid';
  payment_amount: number;
  payment_date: string | null;
}

interface EditValues {
  name?: string;
  payment_status?: 'paid' | 'unpaid';
  payment_amount?: number;
  payment_date?: string;
}

interface MemberCardProps {
  member: Member;
  isEditing: boolean;
  editValues: EditValues;
  toggleLoading: boolean;
  onEdit: (member: Member) => void;
  onDelete: (id: string) => void;
  onToggleStatus: (member: Member) => void;
  onSaveEdit: () => void;
  onCancelEdit: () => void;
  onChange: (field: string, value: string | number) => void;
}

const MemberCard: React.FC<MemberCardProps> = ({
  member,
  isEditing,
  editValues,
  toggleLoading,
  onEdit,
  onDelete,
  onToggleStatus,
  onSaveEdit,
  onCancelEdit,
  onChange
}) => {
  return (
    <div className="bg-white border-4 border-[#5D534B] rounded-xl shadow-[6px_6px_0px_#5D534B] p-4">
      {isEditing ? (
        /* EDIT MODE */
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-2 text-[#5D534B]">Nama:</label>
            <input
              type="text"
              value={editValues.name || ''}
              onChange={(e) => onChange('name', e.target.value)}
              className="w-full px-3 py-2 border-2 border-[#5D534B] rounded-lg focus:outline-none focus:ring-2 focus:ring-[#9DE0D2]"
              placeholder="Masukkan nama anggota"
            />
          </div>

          <div>
            <label htmlFor={`edit-status-${member.id}`} className="block text-sm font-medium mb-2 text-[#5D534B]">Status:</label>
            <select
              id={`edit-status-${member.id}`}
              value={editValues.payment_status || 'unpaid'}
              onChange={(e) => onChange('payment_status', e.target.value)}
              className="w-full px-3 py-2 border-2 border-[#5D534B] rounded-lg focus:outline-none focus:ring-2 focus:ring-[#9DE0D2]"
              aria-label="Status pembayaran anggota"
              title="Pilih status pembayaran"
            >
              <option value="paid">Lunas</option>
              <option value="unpaid">Belum Lunas</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium mb-2 text-[#5D534B]">Jumlah:</label>
            <input
              type="number"
              value={editValues.payment_amount || 0}
              onChange={(e) => onChange('payment_amount', Number(e.target.value))}
              className="w-full px-3 py-2 border-2 border-[#5D534B] rounded-lg focus:outline-none focus:ring-2 focus:ring-[#9DE0D2]"
              placeholder="Masukkan jumlah pembayaran"
            />
          </div>

          <div>
            <label htmlFor={`edit-date-${member.id}`} className="block text-sm font-medium mb-2 text-[#5D534B]">Tanggal:</label>
            <input
              id={`edit-date-${member.id}`}
              type="date"
              value={editValues.payment_date || ''}
              onChange={(e) => onChange('payment_date', e.target.value)}
              className="w-full px-3 py-2 border-2 border-[#5D534B] rounded-lg focus:outline-none focus:ring-2 focus:ring-[#9DE0D2]"
              aria-label="Tanggal pembayaran"
              title="Pilih tanggal pembayaran"
            />
          </div>

          <div className="flex space-x-2 pt-2">
            <button
              type="button"
              className="flex-1 px-3 py-2 bg-[#9DE0D2] border-2 border-[#5D534B] text-sm font-medium text-[#5D534B] rounded-lg hover:bg-[#8DD4C6] transition-colors"
              onClick={onSaveEdit}
            >
              ✅ Simpan
            </button>
            <button
              type="button"
              className="flex-1 px-3 py-2 bg-[#FF9898] border-2 border-[#5D534B] text-sm font-medium text-[#5D534B] rounded-lg hover:bg-[#FF8A8A] transition-colors"
              onClick={onCancelEdit}
            >
              ❌ Batal
            </button>
          </div>
        </div>
      ) : (
        /* VIEW MODE */
        <>
          <div className="flex justify-between items-start mb-3">
            <h3 className="font-bold text-lg text-[#5D534B] break-words">{member.name}</h3>
            <button
              type="button"
              onClick={() => onToggleStatus(member)}
              disabled={toggleLoading}
              className={`px-3 py-1 rounded-full text-sm font-medium border-2 border-[#5D534B] transition-all duration-200 ${
                member.payment_status === 'paid'
                  ? 'bg-[#9DE0D2] text-[#5D534B] hover:bg-[#8DD4C6]'
                  : 'bg-[#FF9898] text-[#5D534B] hover:bg-[#FF8A8A]'
              } ${toggleLoading ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
            >
              {toggleLoading && (
                <Loader2 className="h-4 w-4 animate-spin inline mr-1" />
              )}
              {member.payment_status === 'paid' ? 'Lunas' : 'Belum'}
            </button>
          </div>

          <div className="space-y-2 mb-4">
            <div className="flex justify-between">
              <span className="text-sm text-[#5D534B]">Jumlah:</span>
              <span className="font-bold text-[#5D534B]">{formatRupiah(member.payment_amount)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-[#5D534B]">Tanggal:</span>
              <span className="text-sm text-[#5D534B]">{member.payment_date || '-'}</span>
            </div>
          </div>

          <div className="flex space-x-2">
            <button
              type="button"
              className="flex-1 px-3 py-2 bg-[#9DE0D2] border-2 border-[#5D534B] text-sm font-medium text-[#5D534B] rounded-lg hover:bg-[#8DD4C6] transition-colors"
              onClick={() => onEdit(member)}
            >
              ✏️ Edit
            </button>
            <button
              type="button"
              className="flex-1 px-3 py-2 bg-[#FF9898] border-2 border-[#5D534B] text-sm font-medium text-[#5D534B] rounded-lg hover:bg-[#FF8A8A] transition-colors"
              onClick={() => onDelete(member.id)}
            >
              🗑️ Hapus
            </button>
          </div>
        </>
      )}
    </div>
  );
};

export default MemberCard;
