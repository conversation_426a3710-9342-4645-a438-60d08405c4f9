// Interfaces untuk tipe data
export interface Member {
  id: string;
  name: string;
  email: string;
  phone: string;
  class: string;
  position: string;
  joinDate: string;
  status: 'active' | 'inactive';
  createdAt: string;
  updatedAt: string;
}

export interface Event {
  id: string;
  title: string;
  description: string;
  date: string;
  location: string;
  status: 'upcoming' | 'ongoing' | 'completed';
  createdAt: string;
  updatedAt: string;
}

export interface Transaction {
  id: string;
  type: 'income' | 'expense';
  amount: number;
  description: string;
  category: string;
  date: string;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}

// Semua API akan diimplementasi langsung di komponen menggunakan Firebase
// File ini hanya untuk interface dan tipe data
