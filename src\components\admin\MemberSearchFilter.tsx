interface MemberSearchFilterProps {
  totalMembers: number;
  onSearch: (query: string) => void;
  onFilter: (status: 'all' | 'paid' | 'unpaid') => void;
}

const MemberSearchFilter: React.FC<MemberSearchFilterProps> = ({
  totalMembers,
  onSearch,
  onFilter
}) => {
  return (
    <div className="flex flex-col sm:flex-row gap-4 items-center justify-between bg-white p-4 rounded-lg border-2 border-[#5D534B]">
      <div className="flex flex-col sm:flex-row gap-2 w-full sm:w-auto">
        <input
          type="text"
          placeholder="🔍 Cari nama anggota..."
          onChange={(e) => onSearch(e.target.value)}
          className="px-4 py-3 sm:py-2 border-2 border-[#5D534B] rounded-lg focus:outline-none focus:ring-2 focus:ring-[#9DE0D2] text-base sm:text-sm w-full sm:w-64"
        />
        <select
          onChange={(e) => onFilter(e.target.value as 'all' | 'paid' | 'unpaid')}
          className="px-4 py-3 sm:py-2 border-2 border-[#5D534B] rounded-lg focus:outline-none focus:ring-2 focus:ring-[#9DE0D2] text-base sm:text-sm"
          aria-label="Filter status pembayaran"
        >
          <option value="all">📋 Semua Status</option>
          <option value="paid">✅ Lunas</option>
          <option value="unpaid">❌ Belum Lunas</option>
        </select>
      </div>

      <div className="text-sm text-[#5D534B] font-medium bg-[#9DE0D2] px-3 py-2 rounded-lg border-2 border-black">
        Total: {totalMembers} anggota
      </div>
    </div>
  );
};

export default MemberSearchFilter;
