@import url('https://fonts.googleapis.com/css2?family=Nunito:wght@700;800;900&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 98%;
    --foreground: 33 11% 33%;

    --card: 0 0% 100%;
    --card-foreground: 33 11% 33%;

    --popover: 0 0% 100%;
    --popover-foreground: 33 11% 33%;

    --primary: 0 100% 85%;
    --primary-foreground: 33 11% 33%;

    --secondary: 160 25% 77%;
    --secondary-foreground: 33 11% 33%;

    --muted: 0 0% 96.1%;
    --muted-foreground: 33 11% 33%;

    --accent: 33 11% 33%;
    --accent-foreground: 0 0% 100%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --border: 33 11% 33%;
    --input: 0 0% 89.8%;
    --ring: 33 11% 33%;

    --radius: 1.5rem;

    --sidebar-background: 0 0% 100%;
    --sidebar-foreground: 33 11% 33%;
    --sidebar-primary: 345 100% 66%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 0 0% 96.1%;
    --sidebar-accent-foreground: 33 11% 33%;
    --sidebar-border: 33 11% 33%;
    --sidebar-ring: 33 11% 33%;
  }
}

@layer base {
  html, body {
    @apply text-base sm:text-sm md:text-base lg:text-lg bg-[#F9F9F9];
    scroll-behavior: smooth;
    font-family: 'Nunito', sans-serif;
  }

  * {
    @apply border-border;
  }

  body {
    @apply bg-[#F9F9F9] text-[#5D534B] font-bold;
  }

  * {
    @apply box-border;
  }
}

@layer components {
  .neo-card {
    @apply bg-white border-4 border-[#5D534B] shadow-pastel transition-all duration-200 rounded-2xl;
  }
  
  .neo-button {
    @apply bg-[#FF9898] text-[#5D534B] border-4 border-[#5D534B] shadow-pastel-sm px-3 py-1 text-sm transition-all duration-200 rounded-full;
  }
  
  .neo-button-blue {
    @apply bg-[#9DE0D2] text-[#5D534B] border-4 border-[#5D534B] shadow-pastel-sm px-3 py-1 text-sm transition-all duration-200 rounded-full;
  }
  
  .neo-button-yellow {
    @apply bg-[#FCE09B] text-[#5D534B] border-4 border-[#5D534B] shadow-pastel-sm px-3 py-1 text-sm transition-all duration-200 rounded-full;
  }
  
  .neo-button-green {
    @apply bg-[#9DE0D2] text-[#5D534B] border-4 border-[#5D534B] shadow-pastel-sm px-3 py-1 text-sm transition-all duration-200 rounded-full;
  }
  
  .neo-input {
    @apply bg-white border-4 border-[#5D534B] shadow-pastel-sm px-3 py-1 text-sm focus:outline-none rounded-2xl;
  }
  
  .neo-table {
    @apply w-full border-4 border-[#5D534B] rounded-2xl;
  }
  
  .neo-table th {
    @apply bg-[#9DE0D2] text-[#5D534B] border-4 border-[#5D534B] p-2 text-left;
  }
  
  .neo-table td {
    @apply border-4 border-[#5D534B] p-2;
  }
  
  .neo-table tr:nth-child(even) {
    @apply bg-[#FCE09B] bg-opacity-20;
  }
  
  .neo-gradient-pink {
    @apply bg-gradient-to-br from-white to-[#FF9898]/50;
  }
  
  .neo-gradient-blue {
    @apply bg-gradient-to-br from-white to-[#9DE0D2]/50;
  }
  
  .neo-gradient-yellow {
    @apply bg-gradient-to-br from-white to-[#FCE09B]/50;
  }
  
  .neo-gradient-green {
    @apply bg-gradient-to-br from-white to-[#9DE0D2]/50;
  }

  .expense-card {
    @apply neo-card p-4 mb-3 border-4 border-[#5D534B] neo-gradient-pink;
  }
  
  .income-card {
    @apply neo-card p-4 mb-3 border-4 border-[#5D534B] neo-gradient-green;
  }
  
  .border-neo-pink {
    @apply border-4 border-[#FF9898];
  }
  
  .border-neo-blue {
    @apply border-4 border-[#9DE0D2];
  }
  
  .border-neo-yellow {
    @apply border-4 border-[#FCE09B];
  }
  
  .border-neo-green {
    @apply border-4 border-[#9DE0D2];
  }
}

@layer utilities {
  .responsive-container {
    @apply container mx-auto px-4 sm:px-6 lg:px-8;
  }

  .responsive-grid {
    @apply grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 sm:gap-6;
  }
}

/* Animation Support */
.perspective-1000 {
  perspective: 1000px;
}

.transform-style-preserve-3d {
  transform-style: preserve-3d;
}

/* Page Transitions */
.page-enter {
  opacity: 0;
  transform: translateY(20px);
}

.page-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 300ms, transform 300ms;
}

.page-exit {
  opacity: 1;
}

.page-exit-active {
  opacity: 0;
  transform: translateY(-20px);
  transition: opacity 300ms, transform 300ms;
}

/* Improved animations for neobrutalism style */
@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-5px);
  }
  100% {
    transform: translateY(0px);
  }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

/* Add some prefers-reduced-motion for accessibility */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

