// src/context/AuthContext.tsx
import React, { createContext, useState, useContext, useEffect, ReactNode, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { authAPI, AdminData } from '../services/api'; // Pastikan path api.ts benar
import { User, Session, AuthChangeEvent } from '@supabase/supabase-js'; // Import tipe dari Supabase
import { toast } from 'sonner';

interface AuthContextType {
  isAuthenticated: boolean;
  user: User | null;
  isLoading: boolean;
  login: (adminData: AdminData) => Promise<boolean>; // Kembalikan boolean sukses/gagal
  logout: () => Promise<void>;
}

// Membuat context dengan nilai default (undefined untuk cek apakah provider sudah ada)
const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true); // Mulai dengan loading true
  const navigate = useNavigate();

  // Fungsi untuk memeriksa sesi saat komponen dimuat
  const checkUserSession = useCallback(async () => {
    console.log("Checking user session...");
    setIsLoading(true);
    try {
      const sessionData = await authAPI.getSession(); // Dapatkan session
      console.log("Session data:", sessionData);

      if (sessionData) {
        const userData = await authAPI.getCurrentAdmin(); // Dapatkan data user jika ada session
        console.log("User data from session:", userData);
        if (userData?.user) {
           setUser(userData.user);
           setIsAuthenticated(true);
           console.log("User authenticated from session.");
        } else {
           setUser(null);
           setIsAuthenticated(false);
           await authAPI.logout(); // Logout jika session ada tapi user tidak ada
           console.log("Session exists but no user found, logging out.");
        }
      } else {
        setUser(null);
        setIsAuthenticated(false);
        console.log("No active session found.");
      }
    } catch (error) {
      console.error('Error checking user session:', error);
      setUser(null);
      setIsAuthenticated(false);
      // Mungkin tidak perlu logout paksa jika error jaringan, tapi pastikan state bersih
      // await authAPI.logout();
    } finally {
      setIsLoading(false);
      console.log("Finished checking session. Loading state:", false);
    }
  }, []);

  // Jalankan pemeriksaan sesi saat provider pertama kali dirender
  useEffect(() => {
    checkUserSession();

    // Listener untuk perubahan status autentikasi Supabase
    // Gunakan fungsi baru dari authAPI
    const authSubscription = authAPI.subscribeToAuthStateChange(
      async (event: AuthChangeEvent, session: Session | null) => { // Tambahkan tipe
        console.log(`Supabase auth state changed: ${event}`, session);
        if (event === 'SIGNED_IN' && session?.user) {
          setUser(session.user);
          setIsAuthenticated(true);
        } else if (event === 'SIGNED_OUT') {
          setUser(null);
          setIsAuthenticated(false);
        }
        // Re-check user data jika session berubah tapi bukan sign out
        if (event !== 'SIGNED_OUT') {
           await checkUserSession();
        }
      }
    );

    // Cleanup listener saat komponen unmount
    return () => {
      authSubscription?.subscription.unsubscribe();
    };

  }, [checkUserSession]);


  const login = async (adminData: AdminData): Promise<boolean> => {
    setIsLoading(true);
    try {
      const data = await authAPI.login(adminData.email, adminData.password);
      console.log("Login response data:", data);
      if (data?.user && data?.session) {
        setUser(data.user);
        setIsAuthenticated(true);
        console.log("Login successful.");
        toast.success("Login berhasil!");
        setIsLoading(false);
        return true;
      } else {
         throw new Error("Login failed: No user or session data returned.");
      }
    } catch (error: unknown) { // Ganti any dengan unknown
      console.error('Login error:', error);
      setUser(null);
      setIsAuthenticated(false);
      // Cek tipe error sebelum akses message
      const message = error instanceof Error ? error.message : 'Email atau password salah.';
      toast.error(`Login gagal: ${message}`);
      setIsLoading(false);
      return false;
    }
  };

  const logout = async () => {
    setIsLoading(true);
    try {
      await authAPI.logout();
      setUser(null);
      setIsAuthenticated(false);
      console.log("Logout successful.");
      toast.info("Anda telah logout.");
      navigate('/login'); // Arahkan ke login setelah logout
    } catch (error) {
      console.error('Logout error:', error);
      toast.error("Gagal melakukan logout.");
      // State mungkin sudah direset oleh onAuthStateChange, tapi bisa set manual lagi
      setUser(null);
      setIsAuthenticated(false);
    } finally {
       setIsLoading(false);
    }
  };

  // Nilai yang akan disediakan oleh context
  const value = {
    isAuthenticated,
    user,
    isLoading,
    login,
    logout,
  };

  // Sediakan context ke komponen anak
  // Hanya render children jika loading selesai untuk menghindari state awal yang salah
  // return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
  // Atau bisa juga render children langsung dan biarkan RequireAuth menangani loading
   return (
     <AuthContext.Provider value={value}>
       {children}
     </AuthContext.Provider>
   );
};

// Custom hook untuk menggunakan AuthContext
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};