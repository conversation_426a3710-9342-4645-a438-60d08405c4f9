import React, { Component, ErrorInfo, ReactNode } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>gle, Refresh<PERSON>w, Home } from 'lucide-react';
import { Link } from 'react-router-dom';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
}

class ErrorBoundary extends Component<Props, State> {
  public state: State = {
    hasError: false,
    error: null,
    errorInfo: null
  };

  public static getDerivedStateFromError(error: Error): State {
    return {
      hasError: true,
      error,
      errorInfo: null
    };
  }

  public componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('ErrorBoundary caught an error:', error, errorInfo);

    this.setState({
      error,
      errorInfo
    });

    // Call custom error handler if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }

    // Log to external service in production
    if (process.env.NODE_ENV === 'production') {
      this.logErrorToService(error, errorInfo);
    }
  }

  private logErrorToService = (error: Error, errorInfo: ErrorInfo) => {
    // Placeholder for external error logging
    const errorData = {
      message: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href
    };

    // In production, send this to your error tracking service
    console.error('Error logged to service:', errorData);
  };

  private handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null
    });
  };

  private handleReload = () => {
    window.location.reload();
  };

  public render() {
    if (this.state.hasError) {
      // Custom fallback UI
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // Default error UI
      return (
        <div className="min-h-screen bg-[#F9F9F9] flex items-center justify-center p-4">
          <div className="neo-card max-w-md w-full p-6 text-center">
            <div className="flex justify-center mb-4">
              <div className="p-3 bg-red-100 rounded-full">
                <AlertTriangle size={32} className="text-red-600" />
              </div>
            </div>

            <h2 className="text-xl font-bold text-[#5D534B] mb-2">
              Oops! Terjadi Kesalahan
            </h2>

            <p className="text-[#5D534B] mb-6">
              Aplikasi mengalami masalah yang tidak terduga. Silakan coba lagi atau kembali ke halaman utama.
            </p>

            {/* Error details in development */}
            {process.env.NODE_ENV === 'development' && this.state.error && (
              <details className="mb-6 text-left">
                <summary className="cursor-pointer text-sm font-medium text-[#5D534B] mb-2">
                  Detail Error (Development)
                </summary>
                <div className="bg-gray-100 p-3 rounded text-xs font-mono overflow-auto max-h-32">
                  <div className="text-red-600 font-bold mb-1">
                    {this.state.error.name}: {this.state.error.message}
                  </div>
                  <div className="text-gray-600">
                    {this.state.error.stack}
                  </div>
                </div>
              </details>
            )}

            <div className="flex flex-col sm:flex-row gap-3 justify-center">
              <button
                type="button"
                onClick={this.handleRetry}
                className="flex items-center justify-center px-4 py-2 bg-[#9DE0D2] text-[#5D534B] rounded-lg hover:bg-[#8DD4C6] transition-colors"
              >
                <RefreshCw size={16} className="mr-2" />
                Coba Lagi
              </button>

              <button
                type="button"
                onClick={this.handleReload}
                className="flex items-center justify-center px-4 py-2 border border-[#5D534B] text-[#5D534B] rounded-lg hover:bg-[#5D534B] hover:text-white transition-colors"
              >
                <RefreshCw size={16} className="mr-2" />
                Muat Ulang
              </button>

              <Link
                to="/"
                className="flex items-center justify-center px-4 py-2 bg-[#B39DDB] text-[#5D534B] rounded-lg hover:bg-[#A389D1] transition-colors"
              >
                <Home size={16} className="mr-2" />
                Beranda
              </Link>
            </div>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

// HOC for wrapping components with error boundary
export const withErrorBoundary = <P extends object>(
  Component: React.ComponentType<P>,
  fallback?: ReactNode,
  onError?: (error: Error, errorInfo: ErrorInfo) => void
) => {
  const WrappedComponent = (props: P) => (
    <ErrorBoundary fallback={fallback} onError={onError}>
      <Component {...props} />
    </ErrorBoundary>
  );

  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`;

  return WrappedComponent;
};

// Specific error boundaries for different contexts
export const PageErrorBoundary: React.FC<{ children: ReactNode }> = ({ children }) => (
  <ErrorBoundary
    onError={(error, errorInfo) => {
      console.error('Page Error:', error, errorInfo);
    }}
  >
    {children}
  </ErrorBoundary>
);

export const ComponentErrorBoundary: React.FC<{
  children: ReactNode;
  componentName?: string;
}> = ({ children, componentName = 'Component' }) => (
  <ErrorBoundary
    fallback={
      <div className="neo-card p-4 text-center">
        <AlertTriangle size={24} className="text-red-600 mx-auto mb-2" />
        <p className="text-[#5D534B]">
          {componentName} mengalami masalah. Silakan muat ulang halaman.
        </p>
      </div>
    }
    onError={(error, errorInfo) => {
      console.error(`${componentName} Error:`, error, errorInfo);
    }}
  >
    {children}
  </ErrorBoundary>
);

export default ErrorBoundary;