import React, { useState } from 'react';
import { Trash, Plus, Save } from 'lucide-react';
import { formatRupiah, formatDate } from '../../utils/formatters';
import { Input } from '../../components/ui/input';
import { Textarea } from '../../components/ui/textarea';
import { Button } from '../../components/ui/button';
import { toast } from 'sonner';
import Loader from '../../components/Loader';
import PageTitle from '../../components/PageTitle';
import { useExpensesContext } from '../../context/ExpensesContext';
import { handleError, withRetry } from '../../utils/errorHandler';

// Types
interface NewExpense {
  description: string;
  amount: number;
  date: string;
  category: string;
}

const AdminExpensesPage: React.FC = () => {
  // Context
  const { expenses, loading, addExpense, deleteExpense } = useExpensesContext();

  // State
  const [isAdding, setIsAdding] = useState(false);
  const [newExpense, setNewExpense] = useState<NewExpense>({
    description: '',
    amount: 0,
    date: new Date().toISOString().split('T')[0],
    category: ''
  });
  const [actionLoading, setActionLoading] = useState(false);

  // Event Handlers
  const handleAdd = async () => {
    try {
      setActionLoading(true);

      await withRetry(
        () => addExpense({
          description: newExpense.description,
          amount: newExpense.amount,
          date: newExpense.date,
          category: newExpense.category,
          type: 'expense',
          createdBy: 'admin'
        }),
        3,
        1000,
        'Add Expense'
      );

      toast.success('✅ Pengeluaran berhasil ditambahkan');

      // Reset form
      setNewExpense({
        description: '',
        amount: 0,
        date: new Date().toISOString().split('T')[0],
        category: ''
      });
      setIsAdding(false);
    } catch (error) {
      handleError(error, 'Add Expense');
    } finally {
      setActionLoading(false);
    }
  };

  const handleDelete = async (id: string) => {
    if (!window.confirm('Yakin ingin menghapus pengeluaran ini?')) return;

    try {
      setActionLoading(true);
      await deleteExpense(id);
      toast.success('✅ Pengeluaran berhasil dihapus');
    } catch (error) {
      handleError(error, 'Delete Expense');
    } finally {
      setActionLoading(false);
    }
  };

  // Loading State
  if (loading) {
    return (
      <div className="flex justify-center items-center py-8">
        <Loader size="medium" text="Memuat Data Pengeluaran..." />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <PageTitle title="Kelola Pengeluaran" />
        <Button
          type="button"
          onClick={() => setIsAdding(true)}
          className="neo-button-pink text-sm flex items-center"
          disabled={isAdding}
        >
          <Plus size={16} className="mr-2" />
          Tambah Pengeluaran
        </Button>
      </div>

      {/* Add Expense Form */}
      {isAdding && (
        <div className="bg-white p-6 rounded-lg border-2 border-[#5D534B]">
          <h3 className="text-lg font-bold mb-4">Tambah Pengeluaran Baru</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            <div className="md:col-span-2">
              <label htmlFor="description" className="block text-sm font-medium mb-2">Deskripsi</label>
              <Input
                id="description"
                value={newExpense.description}
                onChange={(e) => setNewExpense({ ...newExpense, description: e.target.value })}
                className="h-12"
                placeholder="Contoh: Pembelian ATK"
                required
              />
            </div>
            <div>
              <label htmlFor="amount" className="block text-sm font-medium mb-2">Jumlah (Rp)</label>
              <Input
                id="amount"
                type="number"
                value={newExpense.amount || ''}
                onChange={(e) => setNewExpense({ ...newExpense, amount: Number(e.target.value) || 0 })}
                className="h-12"
                placeholder="0"
                required
              />
            </div>
            <div>
              <label htmlFor="date" className="block text-sm font-medium mb-2">Tanggal</label>
              <Input
                id="date"
                type="date"
                value={newExpense.date}
                onChange={(e) => setNewExpense({ ...newExpense, date: e.target.value })}
                className="h-12"
                required
              />
            </div>
            <div className="md:col-span-2">
              <label htmlFor="category" className="block text-sm font-medium mb-2">Kategori (Opsional)</label>
              <Textarea
                id="category"
                value={newExpense.category}
                onChange={(e) => setNewExpense({ ...newExpense, category: e.target.value })}
                placeholder="Kategori pengeluaran..."
                rows={3}
              />
            </div>
          </div>
          <div className="flex justify-end space-x-2">
            <Button
              type="button"
              className="px-4 py-2 bg-[#FF9898] border-2 border-[#5D534B] rounded-lg font-medium"
              onClick={handleAdd}
              disabled={!newExpense.description || newExpense.amount <= 0 || !newExpense.date || actionLoading}
            >
              {actionLoading ? (
                <>
                  <Save size={16} className="mr-2 animate-spin" />
                  Menyimpan...
                </>
              ) : (
                <>
                  <Save size={16} className="mr-2" />
                  Simpan
                </>
              )}
            </Button>
            <Button
              type="button"
              className="px-4 py-2 bg-gray-200 border-2 border-[#5D534B] rounded-lg font-medium"
              onClick={() => setIsAdding(false)}
            >
              Batal
            </Button>
          </div>
        </div>
      )}

      {/* Expenses Table */}
      {expenses.length === 0 ? (
        <div className="text-center p-8 bg-white rounded-lg border-2 border-[#5D534B]">
          <p className="text-[#5D534B] text-lg">Belum ada data pengeluaran</p>
          <p className="text-[#5D534B] opacity-70 text-sm mt-2">Tambah pengeluaran pertama Anda</p>
        </div>
      ) : (
        <div className="bg-white rounded-lg border-2 border-[#5D534B] overflow-hidden">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-[#F9F9F9] border-b-2 border-[#5D534B]">
                <tr>
                  <th className="text-left py-4 px-4 font-bold text-[#5D534B]">Deskripsi</th>
                  <th className="text-left py-4 px-4 font-bold text-[#5D534B]">Tanggal</th>
                  <th className="text-left py-4 px-4 font-bold text-[#5D534B]">Jumlah</th>
                  <th className="text-left py-4 px-4 font-bold text-[#5D534B]">Kategori</th>
                  <th className="text-center py-4 px-4 font-bold text-[#5D534B]">Aksi</th>
                </tr>
              </thead>
              <tbody>
                {expenses.map((expense, index) => (
                  <tr
                    key={expense.id}
                    className={`border-b border-[#5D534B] hover:bg-[#F9F9F9] ${
                      index % 2 === 0 ? 'bg-white' : 'bg-[#FAFAFA]'
                    }`}
                  >
                    <td className="py-3 px-4 text-[#5D534B]">{expense.description}</td>
                    <td className="py-3 px-4 text-[#5D534B]">{formatDate(expense.date)}</td>
                    <td className="py-3 px-4 text-[#5D534B] font-bold">{formatRupiah(expense.amount)}</td>
                    <td className="py-3 px-4 text-[#5D534B]">{expense.category || '-'}</td>
                    <td className="py-3 px-4 text-center">
                      <Button
                        type="button"
                        onClick={() => handleDelete(expense.id)}
                        className="px-3 py-1 bg-[#FF9898] border-2 border-[#5D534B] rounded-lg text-[#5D534B] hover:bg-[#FF7777]"
                        disabled={actionLoading}
                        title="Hapus pengeluaran"
                      >
                        <Trash size={16} />
                      </Button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}
    </div>
  );
};

export default AdminExpensesPage;
