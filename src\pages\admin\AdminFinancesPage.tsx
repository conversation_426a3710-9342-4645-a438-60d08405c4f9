import { useState, useEffect } from 'react';
import { transactionAPI, Transaction } from '../../services/api';
import { formatRupiah, formatDate } from '../../utils/formatters';
import { toast } from '@/components/ui/use-toast';
import Loader from '../../components/Loader';
import { handleError } from '../../utils/errorHandler';

const AdminFinancesPage = () => {
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showAddModal, setShowAddModal] = useState(false);
  const [newTransaction, setNewTransaction] = useState<Partial<Transaction>>({
    type: 'expense',
    description: '',
    amount: 0,
    date: new Date().toISOString().split('T')[0],
    notes: ''
  });

  useEffect(() => {
    const loadData = async () => {
      try {
        setIsLoading(true);
        const data = await transactionAPI.getAll();
        setTransactions(data);
      } catch (error) {
        handleError(error, 'AdminFinancesPage');
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, []);

  const handleDelete = async (id: string) => {
    if (window.confirm('Apakah Anda yakin ingin menghapus transaksi ini?')) {
      try {
        await transactionAPI.delete(id);
        setTransactions(transactions.filter(transaction => transaction.id !== id));
        toast({
          title: "Sukses",
          description: "Data transaksi berhasil dihapus",
        });
      } catch (error) {
        handleError(error, 'AdminFinancesPage');
      }
    }
  };

  const handleAdd = async () => {
    try {
      await transactionAPI.create(newTransaction as Transaction);
      setShowAddModal(false);
      setNewTransaction({
        type: 'expense',
        description: '',
        amount: 0,
        date: new Date().toISOString().split('T')[0],
        notes: ''
      });
      loadTransactions();
    } catch (error) {
      handleError(error, 'AdminFinancesPage');
    }
  };

  const loadTransactions = async () => {
    try {
      setIsLoading(true);
      const data = await transactionAPI.getAll();
      setTransactions(data);
    } catch (error) {
      handleError(error, 'AdminFinancesPage');
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <Loader size="medium" variant="primary" text="Memuat Data Keuangan..." />
      </div>
    );
  }

  return (
    <div className="bg-[#F9F9F9] text-[#5D534B] rounded-2xl">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl md:text-3xl font-bold border-b-4 border-[#FF9898] pb-2">Kelola Keuangan</h1>
        <button
          onClick={() => setShowAddModal(true)}
          className="px-4 py-2 bg-[#FF9898] text-white rounded-lg hover:bg-[#FF7A7A] transition-colors"
        >
          Tambah Pengeluaran
        </button>
      </div>

      {isLoading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#FF9898]"></div>
        </div>
      ) : (
        <div className="neo-card p-4 overflow-hidden animate-fade-in bg-white border-4 border-[#9DE0D2]">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b-2 border-[#5D534B]">
                  <th className="text-left py-3 px-4">Deskripsi</th>
                  <th className="text-left py-3 px-4">Tanggal</th>
                  <th className="text-left py-3 px-4">Jumlah</th>
                  <th className="text-left py-3 px-4">Keterangan</th>
                  <th className="text-left py-3 px-4">Aksi</th>
                </tr>
              </thead>
              <tbody>
                {transactions
                  .filter(transaction => transaction.type === 'expense')
                  .map((transaction) => (
                    <tr key={transaction.id} className="border-b border-[#5D534B]/20">
                      <td className="py-3 px-4">{transaction.description}</td>
                      <td className="py-3 px-4">{formatDate(transaction.date)}</td>
                      <td className="py-3 px-4">{formatRupiah(transaction.amount)}</td>
                      <td className="py-3 px-4">{transaction.notes || '-'}</td>
                      <td className="py-3 px-4">
                        <button
                          onClick={() => handleDelete(transaction.id)}
                          className="text-red-500 hover:text-red-700"
                        >
                          Hapus
                        </button>
                      </td>
                    </tr>
                  ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {showAddModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center">
          <div className="bg-white p-6 rounded-lg w-full max-w-md">
            <h2 className="text-xl font-bold mb-4">Tambah Pengeluaran</h2>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-1" htmlFor="description">Deskripsi</label>
                <input
                  id="description"
                  type="text"
                  value={newTransaction.description}
                  onChange={(e) => setNewTransaction({ ...newTransaction, description: e.target.value })}
                  className="w-full p-2 border rounded"
                  placeholder="Masukkan deskripsi pengeluaran"
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1" htmlFor="amount">Jumlah</label>
                <input
                  id="amount"
                  type="number"
                  value={newTransaction.amount}
                  onChange={(e) => setNewTransaction({ ...newTransaction, amount: Number(e.target.value) })}
                  className="w-full p-2 border rounded"
                  placeholder="Masukkan jumlah pengeluaran"
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1" htmlFor="date">Tanggal</label>
                <input
                  id="date"
                  type="date"
                  value={newTransaction.date}
                  onChange={(e) => setNewTransaction({ ...newTransaction, date: e.target.value })}
                  className="w-full p-2 border rounded"
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1" htmlFor="notes">Keterangan</label>
                <input
                  id="notes"
                  type="text"
                  value={newTransaction.notes}
                  onChange={(e) => setNewTransaction({ ...newTransaction, notes: e.target.value })}
                  className="w-full p-2 border rounded"
                  placeholder="Masukkan keterangan tambahan"
                />
              </div>
              <div className="flex justify-end space-x-2">
                <button
                  onClick={() => setShowAddModal(false)}
                  className="px-4 py-2 border rounded hover:bg-gray-100"
                >
                  Batal
                </button>
                <button
                  onClick={handleAdd}
                  className="px-4 py-2 bg-[#FF9898] text-white rounded hover:bg-[#FF7A7A]"
                >
                  Tambah
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AdminFinancesPage;
