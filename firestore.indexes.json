{"indexes": [{"collectionGroup": "members", "queryScope": "COLLECTION", "fields": [{"fieldPath": "payment_status", "order": "ASCENDING"}, {"fieldPath": "name", "order": "ASCENDING"}]}, {"collectionGroup": "expenses", "queryScope": "COLLECTION", "fields": [{"fieldPath": "date", "order": "DESCENDING"}, {"fieldPath": "amount", "order": "DESCENDING"}]}, {"collectionGroup": "events", "queryScope": "COLLECTION", "fields": [{"fieldPath": "date", "order": "DESCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}]}], "fieldOverrides": []}