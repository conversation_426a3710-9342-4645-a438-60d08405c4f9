{"ci": {"collect": {"url": ["http://localhost:4173/", "http://localhost:4173/admin", "http://localhost:4173/members", "http://localhost:4173/events", "http://localhost:4173/finance"], "startServerCommand": "npm run preview", "startServerReadyPattern": "Local:.*4173", "startServerReadyTimeout": 30000, "numberOfRuns": 3}, "assert": {"assertions": {"categories:performance": ["warn", {"minScore": 0.8}], "categories:accessibility": ["error", {"minScore": 0.9}], "categories:best-practices": ["warn", {"minScore": 0.9}], "categories:seo": ["warn", {"minScore": 0.8}], "categories:pwa": ["warn", {"minScore": 0.8}]}}, "upload": {"target": "temporary-public-storage"}}}