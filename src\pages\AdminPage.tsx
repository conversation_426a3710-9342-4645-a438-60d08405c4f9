import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  User, Calendar, Wallet, Users, Settings,
  TrendingDown, Activity, CreditCard
} from 'lucide-react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import { useMembersContext } from '../context/MembersContext';
import { useExpensesContext } from '../context/ExpensesContext';
import { pageTransition } from '../constants';
import PageTitle from '../components/PageTitle';
import StatCard from '../components/StatCard';
import { formatRupiah, formatDate } from '../utils/formatters';
import { handleError } from '../utils/errorHandler';
import { StatCardSkeleton } from '../components/LoadingStates';

interface Activity {
  type: 'event' | 'member' | 'expense';
  title: string;
  date: string;
  icon: React.ElementType;
}

interface DashboardStats {
  totalMembers: number;
  totalEvents: number;
  totalIncome: number;
  totalExpense: number;
  totalDues: number;
  netBalance: number;
}

const AdminPage: React.FC = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [stats, setStats] = useState<DashboardStats>({
    totalMembers: 0,
    totalEvents: 0,
    totalIncome: 0,
    totalExpense: 0,
    totalDues: 0,
    netBalance: 0
  });
  const [recentActivities, setRecentActivities] = useState<Activity[]>([]);
  const navigate = useNavigate();
  const { user, logout } = useAuth();
  const { members, loading: membersLoading } = useMembersContext();
  const { expenses, getTotalExpenses, loading: expensesLoading } = useExpensesContext();

  const handleLogout = async () => {
    try {
      await logout();
      navigate('/admin/login');
    } catch (error) {
      handleError(error, 'AdminPage');
    }
  };

  useEffect(() => {
    const loadAdminData = async () => {
      try {
        setIsLoading(true);
        if (user) {
          await loadDashboardData();
        } else {
          await handleLogout();
        }
      } catch (error) {
        handleError(error, 'AdminPage');
        await handleLogout();
      } finally {
        setIsLoading(false);
      }
    };

    loadAdminData();
  }, [user]);

  // Update stats ketika data members berubah
  useEffect(() => {
    if (!membersLoading) {
      loadDashboardData();
    }
  }, [members, membersLoading]);

  // Update stats ketika data expenses berubah
  useEffect(() => {
    if (!expensesLoading) {
      loadDashboardData();
    }
  }, [expenses, expensesLoading]);

  const loadDashboardData = async () => {
    try {
      // Hitung total iuran dari semua anggota
      const totalDues = members.reduce((sum, member) => sum + member.payment_amount, 0);

      // Hitung total pengeluaran dari ExpensesContext
      const totalExpense = getTotalExpenses();

      setStats({
        totalMembers: members.length,
        totalEvents: 0, // TODO: Implementasi events
        totalIncome: totalDues,
        totalExpense: totalExpense,
        totalDues: totalDues,
        netBalance: totalDues - totalExpense
      });

      // Aktivitas terbaru - gabungkan member dan expenses
      const recentActivitiesData: Activity[] = [
        {
          type: 'member',
          title: `${members.length} anggota terdaftar`,
          date: new Date().toISOString(),
          icon: Users
        },
        // Tambahkan 3 pengeluaran terbaru
        ...expenses.slice(-3).map(expense => ({
          type: 'expense' as const,
          title: `${expense.description} - ${formatRupiah(expense.amount)}`,
          date: expense.date,
          icon: TrendingDown
        }))
      ];

      setRecentActivities(recentActivitiesData);
    } catch (error) {
      handleError(error, 'AdminPage');
    }
  };

  const menuItems = [
    {
      title: "Anggota",
      path: "/admin/members",
      icon: Users,
      color: "#9DE0D2",
      description: "Kelola data anggota"
    },
    {
      title: "Pengaturan Iuran",
      path: "/admin/dues-settings",
      icon: Settings,
      color: "#B39DDB",
      description: "Atur jumlah iuran anggota"
    },
    {
      title: "Pengeluaran",
      path: "/admin/expenses",
      icon: Wallet,
      color: "#FCE09B",
      description: "Kelola pengeluaran"
    },
    {
      title: "Acara",
      path: "/admin/events",
      icon: Calendar,
      color: "#FF9898",
      description: "Kelola acara dan kegiatan"
    }
  ];

  if (isLoading || membersLoading || expensesLoading) {
    return (
      <motion.div
        className="min-h-screen w-full px-4 sm:px-6 py-8 sm:py-10 bg-[#F9F9F9] text-[#5D534B]"
        initial="initial"
        animate="animate"
        exit="exit"
        variants={pageTransition}
      >
        <div className="max-w-7xl mx-auto">
          {/* Header skeleton */}
          <div className="flex flex-col sm:flex-row justify-between items-center mb-6 sm:mb-8">
            <div className="flex items-center space-x-3 mb-4 sm:mb-0">
              <div className="w-8 h-8 bg-gray-300 rounded-full animate-pulse"></div>
              <div className="w-32 h-4 bg-gray-300 rounded animate-pulse"></div>
            </div>
            <div className="w-20 h-8 bg-gray-300 rounded animate-pulse"></div>
          </div>

          {/* Stats skeleton */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6 sm:mb-8">
            <StatCardSkeleton />
            <StatCardSkeleton />
            <StatCardSkeleton />
          </div>

          {/* Content skeleton */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-1">
              <div className="w-32 h-6 bg-gray-300 rounded mb-4 animate-pulse"></div>
              <div className="bg-white p-4 rounded-lg border border-[#5D534B]/10 shadow-sm min-h-[200px]">
                <div className="space-y-3">
                  {Array.from({ length: 3 }).map((_, i) => (
                    <div key={i} className="flex items-start p-2">
                      <div className="w-8 h-8 bg-gray-300 rounded-full mr-3 animate-pulse"></div>
                      <div className="flex-1 space-y-2">
                        <div className="w-3/4 h-4 bg-gray-300 rounded animate-pulse"></div>
                        <div className="w-1/2 h-3 bg-gray-300 rounded animate-pulse"></div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            <div className="lg:col-span-2">
              <div className="w-24 h-6 bg-gray-300 rounded mb-4 animate-pulse"></div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {Array.from({ length: 4 }).map((_, i) => (
                  <div key={i} className="bg-white p-4 sm:p-5 rounded-lg border border-[#5D534B]/10 shadow-sm">
                    <div className="flex items-start space-x-3 sm:space-x-4">
                      <div className="w-10 h-10 sm:w-12 sm:h-12 bg-gray-300 rounded-full animate-pulse"></div>
                      <div className="flex-1 space-y-2">
                        <div className="w-3/4 h-5 bg-gray-300 rounded animate-pulse"></div>
                        <div className="w-full h-4 bg-gray-300 rounded animate-pulse"></div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </motion.div>
    );
  }

  if (!user) {
     return null;
  }

  return (
    <motion.div 
      className="min-h-screen w-full px-4 sm:px-6 py-8 sm:py-10 bg-[#F9F9F9] text-[#5D534B]"
      initial="initial"
      animate="animate"
      exit="exit"
      variants={pageTransition}
    >
      <PageTitle title="Dashboard Admin" />

      <div className="max-w-7xl mx-auto">
        <div className="flex flex-col sm:flex-row justify-between items-center mb-6 sm:mb-8">
          <div className="flex items-center space-x-3 mb-4 sm:mb-0">
            <div className="p-2 bg-[#DDD6F3] rounded-full">
              <User size={20} className="text-[#5D534B]" />
            </div>
            <span className="font-medium text-[#5D534B] text-sm sm:text-base">{user.email}</span>
          </div>
          
          <button
            type="button"
            onClick={handleLogout}
            className="px-4 py-2 border border-[#B39DDB] text-[#5D534B] rounded-lg hover:bg-[#F3F0FB] transition-colors text-sm sm:text-base"
          >
            Logout
          </button>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6 sm:mb-8">
          <StatCard 
            title="Total Iuran Anggota" 
            value={formatRupiah(stats.totalDues)} 
            icon={Users}
            gradientClass="neo-gradient-blue" 
            borderColor="border-neo-blue"
          />
          <StatCard 
            title="Total Pengeluaran" 
            value={formatRupiah(stats.totalExpense)} 
            icon={TrendingDown}
            gradientClass="neo-gradient-pink" 
            borderColor="border-neo-pink"
          />
          <StatCard 
            title="Sisa Saldo" 
            value={formatRupiah(stats.netBalance)} 
            icon={CreditCard}
            gradientClass="neo-gradient-yellow" 
            borderColor="border-neo-yellow"
          />
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-1">
            <h3 className="text-base sm:text-lg font-bold mb-3 sm:mb-4 text-[#5D534B]">Aktivitas Terbaru</h3>
            <div className="bg-white p-4 rounded-lg border border-[#5D534B]/10 shadow-sm min-h-[200px]">
              {recentActivities.length > 0 ? (
                <div className="space-y-3">
                  {recentActivities.map((activity, index) => (
                    <div key={index} className="flex items-start p-2 rounded-md">
                      <div className="w-8 h-8 flex-shrink-0 flex items-center justify-center rounded-full mr-3 bg-[#F9F9F9]">
                        <activity.icon size={16} className="text-[#5D534B]" />
                      </div>
                      <div className="overflow-hidden">
                        <p className="text-[#5D534B] font-medium text-sm truncate" title={activity.title}>{activity.title}</p>
                        <p className="text-xs text-[#5D534B]/70">{formatDate(activity.date)}</p>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-center text-[#5D534B]/70 py-4 text-sm">Tidak ada aktivitas terbaru</p>
              )}
            </div>
          </div>

          <div className="lg:col-span-2">
            <h3 className="text-base sm:text-lg font-bold mb-3 sm:mb-4 text-[#5D534B]">Menu Utama</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {menuItems.map((item, index) => (
                <Link 
                  key={index} 
                  to={item.path} 
                  className="bg-white p-4 sm:p-5 rounded-lg border border-[#5D534B]/10 shadow-sm hover:shadow-md hover:border-[#5D534B]/30 transition-all duration-200 flex items-start space-x-3 sm:space-x-4"
                >
                  <div className="w-10 h-10 sm:w-12 sm:h-12 flex-shrink-0 flex items-center justify-center rounded-full" style={{ backgroundColor: `${item.color}40` }}>
                    <item.icon size={22} style={{ color: item.color }} />
                  </div>
                  <div className="overflow-hidden">
                    <h3 className="font-bold text-[#5D534B] text-base sm:text-lg truncate" title={item.title}>{item.title}</h3>
                    <p className="text-sm text-[#5D534B]/70">{item.description}</p>
                  </div>
                </Link>
              ))}
            </div>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export default AdminPage; 