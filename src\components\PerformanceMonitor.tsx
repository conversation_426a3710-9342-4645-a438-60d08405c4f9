import React, { useState, useEffect } from 'react';
import { Zap, Clock, Wifi, HardDrive } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';

interface PerformanceMetrics {
  loadTime: number;
  cacheHitRate: number;
  networkSpeed: 'fast' | 'slow' | 'offline';
  memoryUsage: number;
}

const PerformanceMonitor: React.FC = () => {
  const [showMetrics, setShowMetrics] = useState(false);
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    loadTime: 0,
    cacheHitRate: 0,
    networkSpeed: 'fast',
    memoryUsage: 0
  });

  useEffect(() => {
    // Measure page load time
    const measureLoadTime = () => {
      if (performance.timing) {
        const loadTime = performance.timing.loadEventEnd - performance.timing.navigationStart;
        setMetrics(prev => ({ ...prev, loadTime: loadTime / 1000 }));
      }
    };

    // Measure network speed
    const measureNetworkSpeed = () => {
      const connection = (navigator as any).connection || (navigator as any).mozConnection || (navigator as any).webkitConnection;
      if (connection) {
        const speed = connection.effectiveType;
        let networkSpeed: 'fast' | 'slow' | 'offline' = 'fast';
        
        if (speed === 'slow-2g' || speed === '2g') {
          networkSpeed = 'slow';
        } else if (speed === '3g' || speed === '4g') {
          networkSpeed = 'fast';
        }
        
        if (!navigator.onLine) {
          networkSpeed = 'offline';
        }
        
        setMetrics(prev => ({ ...prev, networkSpeed }));
      }
    };

    // Measure memory usage (if available)
    const measureMemoryUsage = () => {
      if ((performance as any).memory) {
        const memory = (performance as any).memory;
        const usagePercent = (memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100;
        setMetrics(prev => ({ ...prev, memoryUsage: usagePercent }));
      }
    };

    // Calculate cache hit rate
    const calculateCacheHitRate = () => {
      const cacheHits = parseInt(localStorage.getItem('osis-cache-hits') || '0', 10);
      const totalRequests = parseInt(localStorage.getItem('osis-total-requests') || '0', 10);
      const hitRate = totalRequests > 0 ? (cacheHits / totalRequests) * 100 : 0;
      setMetrics(prev => ({ ...prev, cacheHitRate: hitRate }));
    };

    // Initial measurements
    setTimeout(() => {
      measureLoadTime();
      measureNetworkSpeed();
      measureMemoryUsage();
      calculateCacheHitRate();
    }, 1000);

    // Update network speed on connection change
    const handleConnectionChange = () => {
      measureNetworkSpeed();
    };

    window.addEventListener('online', handleConnectionChange);
    window.addEventListener('offline', handleConnectionChange);

    return () => {
      window.removeEventListener('online', handleConnectionChange);
      window.removeEventListener('offline', handleConnectionChange);
    };
  }, []);

  const getPerformanceColor = (value: number, type: 'time' | 'percentage') => {
    if (type === 'time') {
      if (value < 2) return 'text-green-600';
      if (value < 5) return 'text-yellow-600';
      return 'text-red-600';
    } else {
      if (value > 80) return 'text-green-600';
      if (value > 50) return 'text-yellow-600';
      return 'text-red-600';
    }
  };

  const getNetworkIcon = () => {
    switch (metrics.networkSpeed) {
      case 'fast': return <Wifi className="text-green-600" size={16} />;
      case 'slow': return <Wifi className="text-yellow-600" size={16} />;
      case 'offline': return <Wifi className="text-red-600" size={16} />;
      default: return <Wifi className="text-gray-600" size={16} />;
    }
  };

  return (
    <>
      {/* Performance Monitor Toggle */}
      <button
        type="button"
        onClick={() => setShowMetrics(!showMetrics)}
        className="fixed bottom-4 left-4 bg-blue-500 text-white p-2 rounded-full shadow-lg hover:bg-blue-600 transition-colors z-40"
        title="Performance Metrics"
      >
        <Zap size={16} />
      </button>

      {/* Performance Metrics Panel */}
      <AnimatePresence>
        {showMetrics && (
          <motion.div
            initial={{ opacity: 0, x: -100 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -100 }}
            className="fixed bottom-16 left-4 bg-white rounded-lg shadow-xl border border-gray-200 p-4 z-50 min-w-[280px]"
          >
            <div className="flex items-center justify-between mb-3">
              <h3 className="font-semibold text-gray-900 text-sm flex items-center space-x-2">
                <Zap size={16} className="text-blue-500" />
                <span>Performance</span>
              </h3>
              <button
                type="button"
                onClick={() => setShowMetrics(false)}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                ✕
              </button>
            </div>

            <div className="space-y-3">
              {/* Load Time */}
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Clock size={14} className="text-gray-500" />
                  <span className="text-xs text-gray-600">Load Time</span>
                </div>
                <span className={`text-xs font-medium ${getPerformanceColor(metrics.loadTime, 'time')}`}>
                  {metrics.loadTime.toFixed(2)}s
                </span>
              </div>

              {/* Network Speed */}
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  {getNetworkIcon()}
                  <span className="text-xs text-gray-600">Network</span>
                </div>
                <span className="text-xs font-medium capitalize">
                  {metrics.networkSpeed}
                </span>
              </div>

              {/* Cache Hit Rate */}
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <HardDrive size={14} className="text-gray-500" />
                  <span className="text-xs text-gray-600">Cache Hit</span>
                </div>
                <span className={`text-xs font-medium ${getPerformanceColor(metrics.cacheHitRate, 'percentage')}`}>
                  {metrics.cacheHitRate.toFixed(1)}%
                </span>
              </div>

              {/* Memory Usage */}
              {metrics.memoryUsage > 0 && (
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <div className="w-3.5 h-3.5 bg-gray-500 rounded-sm flex items-center justify-center">
                      <div className="w-2 h-2 bg-white rounded-xs"></div>
                    </div>
                    <span className="text-xs text-gray-600">Memory</span>
                  </div>
                  <span className={`text-xs font-medium ${getPerformanceColor(100 - metrics.memoryUsage, 'percentage')}`}>
                    {metrics.memoryUsage.toFixed(1)}%
                  </span>
                </div>
              )}
            </div>

            {/* Performance Tips */}
            <div className="mt-3 pt-3 border-t border-gray-200">
              <div className="text-xs text-gray-500">
                {metrics.loadTime < 2 && (
                  <div className="flex items-center space-x-1 text-green-600">
                    <span>⚡</span>
                    <span>Excellent performance!</span>
                  </div>
                )}
                {metrics.loadTime >= 2 && metrics.loadTime < 5 && (
                  <div className="flex items-center space-x-1 text-yellow-600">
                    <span>⚠️</span>
                    <span>Good performance</span>
                  </div>
                )}
                {metrics.loadTime >= 5 && (
                  <div className="flex items-center space-x-1 text-red-600">
                    <span>🐌</span>
                    <span>Slow loading detected</span>
                  </div>
                )}
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
};

export default PerformanceMonitor;
