import React, { Component, ReactNode } from 'react';

interface Props {
  children: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
}

class FirebaseErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: any) {
    console.error('Firebase Error Boundary caught an error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="min-h-screen flex items-center justify-center bg-[#F9F9F9]">
          <div className="text-center p-8 bg-white rounded-lg border-2 border-[#5D534B] max-w-md">
            <h2 className="text-xl font-bold text-[#5D534B] mb-4">
              🔥 Firebase Connection Error
            </h2>
            <p className="text-[#5D534B] mb-4">
              Tidak dapat terhubung ke database. Kemungkinan masalah:
            </p>
            <ul className="text-left text-sm text-[#5D534B] mb-6 space-y-2">
              <li>• Firebase Rules belum dikonfigurasi</li>
              <li>• Koneksi internet bermasalah</li>
              <li>• Browser memblokir third-party cookies</li>
            </ul>
            <button
              onClick={() => window.location.reload()}
              className="px-4 py-2 bg-[#9DE0D2] border-2 border-[#5D534B] rounded-lg font-medium hover:bg-[#7DD3C0]"
            >
              🔄 Refresh Halaman
            </button>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

export default FirebaseErrorBoundary;
