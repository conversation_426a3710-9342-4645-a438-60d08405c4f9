import React, { ReactNode } from 'react';
import { Database, Wifi, Shield, RefreshCw } from 'lucide-react';
import ErrorBoundary from './ErrorBoundary';

interface Props {
  children: ReactNode;
}

const FirebaseErrorFallback: React.FC = () => (
  <div className="min-h-screen flex items-center justify-center bg-[#F9F9F9] p-4">
    <div className="neo-card max-w-lg w-full p-6 text-center">
      <div className="flex justify-center mb-4">
        <div className="p-3 bg-red-100 rounded-full">
          <Database size={32} className="text-red-600" />
        </div>
      </div>

      <h2 className="text-xl font-bold text-[#5D534B] mb-4">
        🔥 Firebase Connection Error
      </h2>

      <p className="text-[#5D534B] mb-6">
        Tidak dapat terhubung ke database. Kemungkinan masalah:
      </p>

      <div className="space-y-3 mb-6">
        <div className="flex items-center text-left p-3 bg-gray-50 rounded-lg">
          <Shield size={20} className="text-red-500 mr-3 flex-shrink-0" />
          <span className="text-sm text-[#5D534B]">Firebase Rules belum dikonfigurasi dengan benar</span>
        </div>

        <div className="flex items-center text-left p-3 bg-gray-50 rounded-lg">
          <Wifi size={20} className="text-red-500 mr-3 flex-shrink-0" />
          <span className="text-sm text-[#5D534B]">Koneksi internet bermasalah atau tidak stabil</span>
        </div>

        <div className="flex items-center text-left p-3 bg-gray-50 rounded-lg">
          <Database size={20} className="text-red-500 mr-3 flex-shrink-0" />
          <span className="text-sm text-[#5D534B]">Browser memblokir third-party cookies atau JavaScript</span>
        </div>
      </div>

      <div className="space-y-3">
        <button
          type="button"
          onClick={() => window.location.reload()}
          className="w-full flex items-center justify-center px-4 py-3 bg-[#9DE0D2] text-[#5D534B] rounded-lg hover:bg-[#8DD4C6] transition-colors font-medium"
        >
          <RefreshCw size={16} className="mr-2" />
          Refresh Halaman
        </button>

        <p className="text-xs text-[#5D534B]/70">
          Jika masalah berlanjut, hubungi administrator sistem
        </p>
      </div>
    </div>
  </div>
);

const FirebaseErrorBoundary: React.FC<Props> = ({ children }) => (
  <ErrorBoundary
    fallback={<FirebaseErrorFallback />}
    onError={(error, errorInfo) => {
      // Log Firebase-specific errors
      console.error('Firebase Error:', error, errorInfo);

      // Check if it's a Firebase-related error
      const isFirebaseError = error.message.includes('Firebase') ||
                             error.message.includes('firestore') ||
                             error.message.includes('auth/') ||
                             error.stack?.includes('firebase');

      if (isFirebaseError) {
        console.error('🔥 Firebase-specific error detected:', {
          message: error.message,
          stack: error.stack,
          componentStack: errorInfo.componentStack
        });
      }
    }}
  >
    {children}
  </ErrorBoundary>
);

export default FirebaseErrorBoundary;
