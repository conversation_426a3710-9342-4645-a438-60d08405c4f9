import { create } from 'zustand';
import { Event } from '../services/api';
import { db } from '../lib/firebase';
import {
  collection,
  getDocs,
  addDoc,
  updateDoc,
  deleteDoc,
  doc,
  serverTimestamp
} from 'firebase/firestore';

interface EventState {
  events: Event[];
  isLoading: boolean;
  error: string | null;
}

interface EventActions {
  fetchEvents: () => Promise<void>;
  addEvent: (event: Omit<Event, 'id' | 'created_at'>) => Promise<void>;
  updateEvent: (id: string, event: Partial<Event>) => Promise<void>;
  deleteEvent: (id: string) => Promise<void>;
}

type EventStore = EventState & EventActions;

export const useEventStore = create<EventStore>((set, get) => ({
  events: [],
  isLoading: false,
  error: null,

  fetchEvents: async () => {
    try {
      set({ isLoading: true, error: null });
      const snapshot = await getDocs(collection(db, 'events'));
      const events: Event[] = snapshot.docs.map(docSnap => ({
        id: docSnap.id,
        ...docSnap.data()
      })) as Event[];
      set({ events, isLoading: false });
    } catch (error) {
      set({
        error: error instanceof Error ? error.message : 'Terjadi kesalahan saat memuat data',
        isLoading: false,
        events: []
      });
    }
  },

  addEvent: async (event: Omit<Event, 'id' | 'createdAt' | 'updatedAt'>) => {
    try {
      set({ isLoading: true, error: null });
      const docRef = await addDoc(collection(db, 'events'), {
        ...event,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      });
      // Fetch ulang agar data sinkron
      await get().fetchEvents();
      set({ isLoading: false });
    } catch (error) {
      set({
        error: error instanceof Error ? error.message : 'Terjadi kesalahan saat menambah event',
        isLoading: false
      });
    }
  },

  updateEvent: async (id: string, event: Partial<Event>) => {
    try {
      set({ isLoading: true, error: null });
      const eventDoc = doc(db, 'events', id);
      await updateDoc(eventDoc, {
        ...event,
        updatedAt: serverTimestamp()
      });
      await get().fetchEvents();
      set({ isLoading: false });
    } catch (error) {
      set({
        error: error instanceof Error ? error.message : 'Terjadi kesalahan saat mengupdate event',
        isLoading: false
      });
    }
  },

  deleteEvent: async (id: string) => {
    try {
      set({ isLoading: true, error: null });
      const eventDoc = doc(db, 'events', id);
      await deleteDoc(eventDoc);
      await get().fetchEvents();
      set({ isLoading: false });
    } catch (error) {
      set({
        error: error instanceof Error ? error.message : 'Terjadi kesalahan saat menghapus event',
        isLoading: false
      });
    }
  }
})); 