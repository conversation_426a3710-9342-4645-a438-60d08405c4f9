import { create } from 'zustand';
import { Member, memberAPI } from '../services/api';

interface MemberStore {
  members: Member[];
  isLoading: boolean;
  error: string | null;
  fetchMembers: () => Promise<void>;
  fetchByStatus: (status: 'all' | 'paid' | 'unpaid') => Promise<void>;
  addMember: (member: Omit<Member, 'id' | 'created_at'>) => Promise<void>;
  updateMember: (id: string, member: Partial<Member>) => Promise<void>;
  deleteMember: (id: string) => Promise<void>;
  markAsPaid: (id: string) => Promise<void>;
  markAsUnpaid: (id: string) => Promise<void>;
}

const useMembersStore = create<MemberStore>((set) => ({
  members: [],
  isLoading: false,
  error: null,

  fetchMembers: async () => {
    set({ isLoading: true, error: null });
    try {
      const members = await memberAPI.getAll();
      set({ members, isLoading: false });
    } catch (error) {
      set({ error: (error as Error).message, isLoading: false });
    }
  },

  fetchByStatus: async (status: 'all' | 'paid' | 'unpaid') => {
    set({ isLoading: true, error: null });
    try {
      const members = await memberAPI.getByStatus(status);
      set({ members, isLoading: false });
    } catch (error) {
      set({ error: (error as Error).message, isLoading: false });
    }
  },

  addMember: async (member: Omit<Member, 'id' | 'created_at'>) => {
    set({ isLoading: true, error: null });
    try {
      const newMember = await memberAPI.create(member);
      set((state: MemberStore) => ({ 
        members: [...state.members, newMember],
        isLoading: false
      }));
    } catch (error) {
      set({ error: (error as Error).message, isLoading: false });
    }
  },

  updateMember: async (id: string, member: Partial<Member>) => {
    set({ isLoading: true, error: null });
    try {
      const updatedMember = await memberAPI.update(id, member);
      set((state: MemberStore) => ({
        members: state.members.map((m: Member) => m.id === id ? updatedMember : m),
        isLoading: false
      }));
    } catch (error) {
      set({ error: (error as Error).message, isLoading: false });
    }
  },

  deleteMember: async (id: string) => {
    set({ isLoading: true, error: null });
    try {
      await memberAPI.delete(id);
      set((state: MemberStore) => ({
        members: state.members.filter((m: Member) => m.id !== id),
        isLoading: false
      }));
    } catch (error) {
      set({ error: (error as Error).message, isLoading: false });
    }
  },

  markAsPaid: async (id: string) => {
    set({ isLoading: true, error: null });
    try {
      const updatedMember = await memberAPI.markAsPaid(id);
      set((state: MemberStore) => ({
        members: state.members.map((m: Member) => m.id === id ? updatedMember : m),
        isLoading: false
      }));
    } catch (error) {
      set({ error: (error as Error).message, isLoading: false });
    }
  },

  markAsUnpaid: async (id: string) => {
    set({ isLoading: true, error: null });
    try {
      const updatedMember = await memberAPI.markAsUnpaid(id);
      set((state: MemberStore) => ({
        members: state.members.map((m: Member) => m.id === id ? updatedMember : m),
        isLoading: false
      }));
    } catch (error) {
      set({ error: (error as Error).message, isLoading: false });
    }
  }
}));

export default useMembersStore; 