import { motion } from 'framer-motion';
import { ReactNode, useState, useEffect } from 'react';

type PageTransitionProps = {
  children: ReactNode;
};

// Variasi animasi yang bisa digunakan secara acak
const pageVariants = [
  // Fade up dengan scale
  {
    initial: { opacity: 0, y: 20, scale: 0.95 },
    in: { opacity: 1, y: 0, scale: 1 },
    out: { opacity: 0, y: -20, scale: 0.95 }
  },
  // Slide dari samping dengan rotate
  {
    initial: { opacity: 0, x: 100, rotate: 2 },
    in: { opacity: 1, x: 0, rotate: 0 },
    out: { opacity: 0, x: -100, rotate: -2 }
  },
  // 3D flip effect
  {
    initial: { opacity: 0, rotateX: 20, rotateY: -20, z: -100 },
    in: { opacity: 1, rotateX: 0, rotateY: 0, z: 0 },
    out: { opacity: 0, rotateX: -20, rotateY: 20, z: -100 }
  },
  // Expand effect
  {
    initial: { opacity: 0, scale: 0.7 },
    in: { opacity: 1, scale: 1 },
    out: { opacity: 0, scale: 1.2 }
  }
];

// Transisi yang berbeda untuk setiap variasi animasi
const pageTransitions = [
  {
    type: 'tween',
    ease: 'anticipate',
    duration: 0.5
  },
  {
    type: 'spring',
    stiffness: 100,
    damping: 15
  },
  {
    type: 'tween',
    ease: 'easeOut',
    duration: 0.7
  },
  {
    type: 'spring',
    stiffness: 200,
    damping: 20,
    mass: 0.8
  }
];

const PageTransition = ({ children }: PageTransitionProps) => {
  // Pilih efek secara acak dari array
  const [variantIndex] = useState(() => Math.floor(Math.random() * pageVariants.length));
  const [transitionDelay, setTransitionDelay] = useState(false);
  
  useEffect(() => {
    // Memberikan delay kecil agar animasi terlihat lebih baik
    const timer = setTimeout(() => {
      setTransitionDelay(true);
    }, 50);
    
    return () => clearTimeout(timer);
  }, []);
  
  return (
    <motion.div
      initial="initial"
      animate={transitionDelay ? "in" : "initial"}
      exit="out"
      variants={pageVariants[variantIndex]}
      transition={pageTransitions[variantIndex]}
      className="w-full perspective-1000 transform-style-preserve-3d"
      style={{
        perspective: '1000px',
        transformStyle: 'preserve-3d',
        willChange: 'transform, opacity'
      }}
    >
      {children}
    </motion.div>
  );
};

export default PageTransition; 