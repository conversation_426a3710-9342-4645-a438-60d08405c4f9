// Preloader utility for critical routes and components
import { ComponentType } from 'react';

interface PreloadableComponent {
  (): Promise<{ default: ComponentType<Record<string, unknown>> }>;
}

class RoutePreloader {
  private preloadedRoutes = new Set<string>();
  private preloadPromises = new Map<string, Promise<{ default: ComponentType<Record<string, unknown>> }>>();

  // Preload critical routes on app start
  preloadCriticalRoutes() {
    // Preload most commonly accessed routes
    this.preloadRoute('finance', () => import('../pages/FinancePage'));
    this.preloadRoute('members', () => import('../pages/MembersPage'));
    
    // Preload admin login for faster access
    this.preloadRoute('admin-login', () => import('../pages/AdminLoginPage'));
  }

  // Preload admin routes when user hovers over admin link
  preloadAdminRoutes() {
    this.preloadRoute('admin-dashboard', () => import('../pages/AdminPage'));
    this.preloadRoute('admin-members', () => import('../pages/admin/AdminMembersPage'));
  }

  // Preload specific route
  preloadRoute(routeName: string, importFn: PreloadableComponent) {
    if (this.preloadedRoutes.has(routeName)) {
      return this.preloadPromises.get(routeName);
    }

    const promise = importFn().catch(() => {
      // Remove from preloaded set so it can be retried
      this.preloadedRoutes.delete(routeName);
      this.preloadPromises.delete(routeName);
      // Re-throw to maintain promise chain
      throw new Error(`Failed to preload route: ${routeName}`);
    });

    this.preloadedRoutes.add(routeName);
    this.preloadPromises.set(routeName, promise);

    return promise;
  }

  // Check if route is preloaded
  isPreloaded(routeName: string): boolean {
    return this.preloadedRoutes.has(routeName);
  }

  // Get preload status
  getPreloadStatus() {
    return {
      preloadedCount: this.preloadedRoutes.size,
      preloadedRoutes: Array.from(this.preloadedRoutes)
    };
  }
}

// Singleton instance
export const routePreloader = new RoutePreloader();

// Preload critical resources on idle
export const preloadOnIdle = () => {
  if ('requestIdleCallback' in window) {
    requestIdleCallback(() => {
      routePreloader.preloadCriticalRoutes();
    });
  } else {
    // Fallback for browsers without requestIdleCallback
    setTimeout(() => {
      routePreloader.preloadCriticalRoutes();
    }, 1000);
  }
};

// Preload on mouse enter (for navigation links)
export const createPreloadHandler = (routeName: string, importFn: PreloadableComponent) => {
  return {
    onMouseEnter: () => {
      routePreloader.preloadRoute(routeName, importFn);
    },
    onFocus: () => {
      routePreloader.preloadRoute(routeName, importFn);
    }
  };
};

// Resource hints for critical assets
export const addResourceHints = () => {
  const head = document.head;

  // Preconnect to Firebase
  const preconnectFirebase = document.createElement('link');
  preconnectFirebase.rel = 'preconnect';
  preconnectFirebase.href = 'https://firestore.googleapis.com';
  head.appendChild(preconnectFirebase);

  // DNS prefetch for Firebase Auth
  const dnsPrefetchAuth = document.createElement('link');
  dnsPrefetchAuth.rel = 'dns-prefetch';
  dnsPrefetchAuth.href = 'https://identitytoolkit.googleapis.com';
  head.appendChild(dnsPrefetchAuth);

  // Preload critical fonts if any
  // const fontPreload = document.createElement('link');
  // fontPreload.rel = 'preload';
  // fontPreload.href = '/fonts/critical-font.woff2';
  // fontPreload.as = 'font';
  // fontPreload.type = 'font/woff2';
  // fontPreload.crossOrigin = 'anonymous';
  // head.appendChild(fontPreload);
};

// Initialize preloader
export const initializePreloader = () => {
  // Add resource hints
  addResourceHints();
  
  // Preload critical routes on idle
  preloadOnIdle();
  
  // Preload admin routes on admin link hover
  const adminLinks = document.querySelectorAll('[data-preload="admin"]');
  adminLinks.forEach(link => {
    link.addEventListener('mouseenter', () => {
      routePreloader.preloadAdminRoutes();
    }, { once: true });
  });
};

// Performance monitoring for preloading
export const preloadMetrics = {
  startTime: performance.now(),
  
  logPreloadTime: (routeName: string) => {
    const endTime = performance.now();
    const duration = endTime - preloadMetrics.startTime;
    // Use console.warn for development metrics (allowed by ESLint)
    console.warn(`📊 Route ${routeName} preloaded in ${duration.toFixed(2)}ms`);
  },
  
  getPreloadStats: () => {
    const status = routePreloader.getPreloadStatus();
    return {
      ...status,
      totalPreloadTime: performance.now() - preloadMetrics.startTime
    };
  }
};
