# Production Environment Variables for OSIS
# Copy these to your deployment platform (Netlify/Vercel)

# Firebase Configuration (Production)
VITE_FIREBASE_API_KEY=your_production_api_key_here
VITE_FIREBASE_AUTH_DOMAIN=your_production_auth_domain_here
VITE_FIREBASE_PROJECT_ID=your_production_project_id_here
VITE_FIREBASE_STORAGE_BUCKET=your_production_storage_bucket_here
VITE_FIREBASE_MESSAGING_SENDER_ID=your_production_messaging_sender_id_here
VITE_FIREBASE_APP_ID=your_production_app_id_here

# Application Configuration
VITE_APP_NAME=OSIS
VITE_APP_VERSION=1.0.0
VITE_APP_ENVIRONMENT=production

# Performance Configuration
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_ERROR_REPORTING=true
VITE_ENABLE_PERFORMANCE_MONITORING=true

# Security Configuration
VITE_ENABLE_SECURITY_HEADERS=true
VITE_ENABLE_CSP=true

# Cache Configuration
VITE_CACHE_VERSION=1.0.0
VITE_ENABLE_ADVANCED_CACHING=true

# Monitoring Configuration
VITE_SENTRY_DSN=your_sentry_dsn_here
VITE_GOOGLE_ANALYTICS_ID=your_ga_id_here
