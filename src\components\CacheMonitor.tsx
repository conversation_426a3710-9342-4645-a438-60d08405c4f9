import React, { useState, useEffect } from 'react';
import { Database, Trash2, RefreshCw, BarChart3, X } from 'lucide-react';
import { useCacheManager } from '../hooks/useSmartFirebaseCache';

interface CacheMonitorProps {
  showMonitor: boolean;
  onClose: () => void;
}

const CacheMonitor: React.FC<CacheMonitorProps> = ({ showMonitor, onClose }) => {
  const { stats, clearCache, updateStats } = useCacheManager();
  const [isClearing, setIsClearing] = useState(false);

  useEffect(() => {
    if (showMonitor) {
      updateStats();
      const interval = setInterval(updateStats, 2000);
      return () => clearInterval(interval);
    }
  }, [showMonitor, updateStats]);

  const handleClearCache = async () => {
    setIsClearing(true);
    try {
      await clearCache();
      await new Promise(resolve => setTimeout(resolve, 500)); // Show loading state
    } finally {
      setIsClearing(false);
    }
  };

  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatPercentage = (value: number): string => {
    return (value * 100).toFixed(1) + '%';
  };

  const getHitRateColor = (hitRate: number): string => {
    if (hitRate >= 0.8) return 'text-green-600';
    if (hitRate >= 0.6) return 'text-yellow-600';
    return 'text-red-600';
  };

  if (!showMonitor) return null;

  return (
    <div className="fixed bottom-4 left-4 bg-white rounded-lg shadow-xl border border-gray-200 p-4 z-50 min-w-[320px] max-w-[400px]">
      <div className="flex items-center justify-between mb-3">
        <h3 className="font-semibold text-gray-900 text-sm flex items-center space-x-2">
          <Database size={16} className="text-blue-500" />
          <span>Cache Monitor</span>
        </h3>
        <button
          type="button"
          onClick={onClose}
          className="text-gray-400 hover:text-gray-600 transition-colors"
          title="Close monitor"
        >
          <X size={16} />
        </button>
      </div>

      <div className="space-y-3">
        {/* Hit Rate */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <BarChart3 size={14} className="text-gray-500" />
            <span className="text-xs text-gray-600">Hit Rate</span>
          </div>
          <span className={`text-xs font-medium ${getHitRateColor(stats.hitRate)}`}>
            {formatPercentage(stats.hitRate)}
          </span>
        </div>

        {/* Memory Hits/Misses */}
        <div className="bg-gray-50 rounded-lg p-3 space-y-2">
          <div className="text-xs font-medium text-gray-700 mb-2">Memory Cache</div>
          
          <div className="flex items-center justify-between">
            <span className="text-xs text-gray-600">Hits</span>
            <span className="text-xs font-medium text-green-600">
              {stats.memoryHits.toLocaleString()}
            </span>
          </div>
          
          <div className="flex items-center justify-between">
            <span className="text-xs text-gray-600">Misses</span>
            <span className="text-xs font-medium text-red-600">
              {stats.memoryMisses.toLocaleString()}
            </span>
          </div>
          
          <div className="flex items-center justify-between">
            <span className="text-xs text-gray-600">Size</span>
            <span className="text-xs font-medium text-blue-600">
              {formatBytes(stats.totalSize)}
            </span>
          </div>
        </div>

        {/* IndexedDB Hits/Misses */}
        <div className="bg-gray-50 rounded-lg p-3 space-y-2">
          <div className="text-xs font-medium text-gray-700 mb-2">IndexedDB Cache</div>
          
          <div className="flex items-center justify-between">
            <span className="text-xs text-gray-600">Hits</span>
            <span className="text-xs font-medium text-green-600">
              {stats.indexedDBHits.toLocaleString()}
            </span>
          </div>
          
          <div className="flex items-center justify-between">
            <span className="text-xs text-gray-600">Misses</span>
            <span className="text-xs font-medium text-red-600">
              {stats.indexedDBMisses.toLocaleString()}
            </span>
          </div>
        </div>

        {/* Total Entries */}
        <div className="flex items-center justify-between">
          <span className="text-xs text-gray-600">Total Entries</span>
          <span className="text-xs font-medium text-gray-900">
            {stats.entryCount.toLocaleString()}
          </span>
        </div>

        {/* Cache Performance Indicator */}
        <div className="bg-blue-50 rounded-lg p-3">
          <div className="text-xs font-medium text-blue-900 mb-1">Performance</div>
          <div className="text-xs text-blue-700">
            {stats.hitRate >= 0.8 && '🚀 Excellent cache performance'}
            {stats.hitRate >= 0.6 && stats.hitRate < 0.8 && '⚡ Good cache performance'}
            {stats.hitRate >= 0.4 && stats.hitRate < 0.6 && '⚠️ Fair cache performance'}
            {stats.hitRate < 0.4 && '🐌 Poor cache performance'}
          </div>
        </div>

        {/* Actions */}
        <div className="flex space-x-2 pt-2 border-t border-gray-200">
          <button
            type="button"
            onClick={updateStats}
            className="flex-1 bg-blue-100 text-blue-700 px-3 py-2 rounded-lg text-xs font-medium hover:bg-blue-200 transition-colors flex items-center justify-center space-x-1"
            title="Refresh stats"
          >
            <RefreshCw size={12} />
            <span>Refresh</span>
          </button>
          
          <button
            type="button"
            onClick={handleClearCache}
            disabled={isClearing}
            className="flex-1 bg-red-100 text-red-700 px-3 py-2 rounded-lg text-xs font-medium hover:bg-red-200 transition-colors flex items-center justify-center space-x-1 disabled:opacity-50"
            title="Clear all cache"
          >
            {isClearing ? (
              <RefreshCw size={12} className="animate-spin" />
            ) : (
              <Trash2 size={12} />
            )}
            <span>{isClearing ? 'Clearing...' : 'Clear'}</span>
          </button>
        </div>

        {/* Cache Tips */}
        <div className="text-xs text-gray-500 bg-gray-50 rounded-lg p-2">
          💡 <strong>Tips:</strong> High hit rate means faster loading. Clear cache if experiencing issues.
        </div>
      </div>
    </div>
  );
};

// Cache Monitor Toggle Button
export const CacheMonitorToggle: React.FC = () => {
  const [showMonitor, setShowMonitor] = useState(false);

  return (
    <>
      <button
        type="button"
        onClick={() => setShowMonitor(!showMonitor)}
        className="fixed bottom-4 left-16 bg-blue-500 text-white p-2 rounded-full shadow-lg hover:bg-blue-600 transition-colors z-40"
        title="Cache Monitor"
      >
        <Database size={16} />
      </button>

      <CacheMonitor 
        showMonitor={showMonitor} 
        onClose={() => setShowMonitor(false)} 
      />
    </>
  );
};

export default CacheMonitor;
