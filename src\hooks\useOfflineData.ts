import { useState, useEffect } from 'react';

interface OfflineData {
  [key: string]: unknown;
}

interface UseOfflineDataReturn {
  isOnline: boolean;
  offlineData: OfflineData;
  saveOfflineData: (key: string, data: unknown) => void;
  getOfflineData: (key: string) => unknown;
  clearOfflineData: () => void;
  syncPendingData: () => Promise<void>;
  pendingOperations: number;
}

export const useOfflineData = (): UseOfflineDataReturn => {
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [offlineData, setOfflineData] = useState<OfflineData>({});
  const [pendingOperations, setPendingOperations] = useState(0);

  useEffect(() => {
    // Load offline data from localStorage
    const loadOfflineData = () => {
      try {
        const stored = localStorage.getItem('osis-offline-data');
        if (stored) {
          setOfflineData(JSON.parse(stored));
        }
        
        const pending = localStorage.getItem('osis-pending-operations');
        if (pending) {
          setPendingOperations(parseInt(pending, 10));
        }
      } catch (error) {
        console.error('Error loading offline data:', error);
      }
    };

    loadOfflineData();

    // Listen for online/offline events
    const handleOnline = () => {
      setIsOnline(true);
      // Auto-sync when back online
      syncPendingData();
    };

    const handleOffline = () => {
      setIsOnline(false);
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  const saveOfflineData = (key: string, data: unknown) => {
    const newOfflineData = { ...offlineData, [key]: data };
    setOfflineData(newOfflineData);
    
    try {
      localStorage.setItem('osis-offline-data', JSON.stringify(newOfflineData));
      
      // Track pending operations if offline
      if (!isOnline) {
        const newPendingCount = pendingOperations + 1;
        setPendingOperations(newPendingCount);
        localStorage.setItem('osis-pending-operations', newPendingCount.toString());
      }
    } catch (error) {
      console.error('Error saving offline data:', error);
    }
  };

  const getOfflineData = (key: string): unknown => {
    return offlineData[key];
  };

  const clearOfflineData = () => {
    setOfflineData({});
    setPendingOperations(0);
    try {
      localStorage.removeItem('osis-offline-data');
      localStorage.removeItem('osis-pending-operations');
    } catch (error) {
      console.error('Error clearing offline data:', error);
    }
  };

  const syncPendingData = async (): Promise<void> => {
    if (!isOnline || pendingOperations === 0) return;

    try {
      // Here you would implement actual sync logic with your backend
      // For now, we'll just simulate sync
      
      // Simulate sync delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Clear pending operations after successful sync
      setPendingOperations(0);
      localStorage.setItem('osis-pending-operations', '0');
      
    } catch (error) {
      console.error('❌ Error syncing data:', error);
    }
  };

  return {
    isOnline,
    offlineData,
    saveOfflineData,
    getOfflineData,
    clearOfflineData,
    syncPendingData,
    pendingOperations
  };
};

// Offline-first data operations
export const offlineOperations = {
  // Cache members data
  cacheMembers: (members: unknown[]) => {
    try {
      localStorage.setItem('osis-cached-members', JSON.stringify(members));
      localStorage.setItem('osis-cache-timestamp', Date.now().toString());
    } catch (error) {
      console.error('Error caching members:', error);
    }
  },

  // Get cached members
  getCachedMembers: (): unknown[] => {
    try {
      const cached = localStorage.getItem('osis-cached-members');
      return cached ? JSON.parse(cached) : [];
    } catch (error) {
      console.error('Error getting cached members:', error);
      return [];
    }
  },

  // Cache events data
  cacheEvents: (events: unknown[]) => {
    try {
      localStorage.setItem('osis-cached-events', JSON.stringify(events));
    } catch (error) {
      console.error('Error caching events:', error);
    }
  },

  // Get cached events
  getCachedEvents: (): unknown[] => {
    try {
      const cached = localStorage.getItem('osis-cached-events');
      return cached ? JSON.parse(cached) : [];
    } catch (error) {
      console.error('Error getting cached events:', error);
      return [];
    }
  },

  // Cache expenses data
  cacheExpenses: (expenses: unknown[]) => {
    try {
      localStorage.setItem('osis-cached-expenses', JSON.stringify(expenses));
    } catch (error) {
      console.error('Error caching expenses:', error);
    }
  },

  // Get cached expenses
  getCachedExpenses: (): unknown[] => {
    try {
      const cached = localStorage.getItem('osis-cached-expenses');
      return cached ? JSON.parse(cached) : [];
    } catch (error) {
      console.error('Error getting cached expenses:', error);
      return [];
    }
  },

  // Check if cache is fresh (less than 1 hour old)
  isCacheFresh: (): boolean => {
    try {
      const timestamp = localStorage.getItem('osis-cache-timestamp');
      if (!timestamp) return false;
      
      const cacheAge = Date.now() - parseInt(timestamp, 10);
      const oneHour = 60 * 60 * 1000; // 1 hour in milliseconds
      
      return cacheAge < oneHour;
    } catch (error) {
      console.error('Error checking cache freshness:', error);
      return false;
    }
  },

  // Clear all cached data
  clearCache: () => {
    try {
      localStorage.removeItem('osis-cached-members');
      localStorage.removeItem('osis-cached-events');
      localStorage.removeItem('osis-cached-expenses');
      localStorage.removeItem('osis-cache-timestamp');
    } catch (error) {
      console.error('Error clearing cache:', error);
    }
  }
};
