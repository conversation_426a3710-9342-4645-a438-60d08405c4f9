<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Security Test - Firebase Rules</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .warning { background-color: #fff3cd; color: #856404; }
        button { margin: 5px; padding: 10px; }
    </style>
</head>
<body>
    <h1>🔒 Firebase Security Rules Test</h1>
    <div id="status">Initializing...</div>
    
    <h2>Tests:</h2>
    <button onclick="testUnauthenticatedRead()">Test Unauthenticated Read</button>
    <button onclick="testUnauthenticatedWrite()">Test Unauthenticated Write</button>
    <button onclick="testAuthentication()">Test Authentication</button>
    
    <div id="results"></div>

    <script type="module">
        import { initializeApp } from 'https://www.gstatic.com/firebasejs/11.8.1/firebase-app.js';
        import { getFirestore, collection, getDocs, addDoc } from 'https://www.gstatic.com/firebasejs/11.8.1/firebase-firestore.js';
        import { getAuth, signInWithEmailAndPassword, signOut } from 'https://www.gstatic.com/firebasejs/11.8.1/firebase-auth.js';

        const firebaseConfig = {
            apiKey: "AIzaSyCyMds8m-KKOR4537ZvY4kipd4aFUbIgyc",
            authDomain: "pemuda-psy.firebaseapp.com",
            projectId: "pemuda-psy",
            storageBucket: "pemuda-psy.firebasestorage.app",
            messagingSenderId: "792680197911",
            appId: "1:792680197911:web:59e978bba5dea4dd419152"
        };

        const app = initializeApp(firebaseConfig);
        const db = getFirestore(app);
        const auth = getAuth(app);

        function addResult(message, type = 'success') {
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = message;
            document.getElementById('results').appendChild(div);
        }

        // Test 1: Unauthenticated read (should work for events, fail for members)
        window.testUnauthenticatedRead = async () => {
            addResult('🔍 Testing unauthenticated read access...', 'warning');
            
            try {
                // Test events (should work - public read)
                const eventsSnapshot = await getDocs(collection(db, 'events'));
                addResult(`✅ Events read: SUCCESS (${eventsSnapshot.size} documents) - Public read allowed`, 'success');
            } catch (error) {
                addResult(`❌ Events read: FAILED - ${error.message}`, 'error');
            }

            try {
                // Test members (should fail - auth required)
                const membersSnapshot = await getDocs(collection(db, 'members'));
                addResult(`❌ Members read: SECURITY BREACH! (${membersSnapshot.size} documents) - Should require auth`, 'error');
            } catch (error) {
                addResult(`✅ Members read: BLOCKED - ${error.code} (Security working correctly)`, 'success');
            }
        };

        // Test 2: Unauthenticated write (should fail for all)
        window.testUnauthenticatedWrite = async () => {
            addResult('✏️ Testing unauthenticated write access...', 'warning');
            
            try {
                await addDoc(collection(db, 'events'), {
                    title: 'Security Test Event',
                    description: 'This should not be created without auth',
                    date: new Date().toISOString()
                });
                addResult('❌ Events write: SECURITY BREACH! - Unauthenticated write succeeded', 'error');
            } catch (error) {
                addResult(`✅ Events write: BLOCKED - ${error.code} (Security working correctly)`, 'success');
            }

            try {
                await addDoc(collection(db, 'members'), {
                    name: 'Security Test Member',
                    payment_status: 'unpaid'
                });
                addResult('❌ Members write: SECURITY BREACH! - Unauthenticated write succeeded', 'error');
            } catch (error) {
                addResult(`✅ Members write: BLOCKED - ${error.code} (Security working correctly)`, 'success');
            }
        };

        // Test 3: Authentication test
        window.testAuthentication = async () => {
            addResult('🔐 Testing authentication...', 'warning');
            
            // Note: This would require a test user account
            addResult('ℹ️ Authentication test requires valid credentials', 'warning');
            addResult('ℹ️ Manual test: Try logging in through the app', 'warning');
        };

        // Initialize
        document.getElementById('status').innerHTML = '✅ Firebase initialized - Ready for testing';
        addResult('🚀 Security test initialized. Click buttons above to run tests.', 'warning');
    </script>
</body>
</html>
